"""MileageData model for vehicle mileage information."""
from typing import Optional
from datetime import datetime
from pydantic import BaseModel, Field, ConfigDict


class MileageData(BaseModel):
    """Embedded document for vehicle mileage data."""
    
    model_config = ConfigDict(populate_by_name=True)
    
    value: Optional[int] = Field(None, description="Mileage value")
    date: Optional[int] = Field(None, description="Date as epoch timestamp")
    timestamp: Optional[int] = Field(None, description="Timestamp value")
    unit: str = Field('km', description="Unit of measurement (km or miles)")
    
    
    def toDict(self) -> dict:
        """Convert to dictionary format."""
        result = {
            'value': self.value,
        }
        
        # Always include date field (epoch timestamp)
        if self.date:
            result['date'] = self.date
        elif self.timestamp:
            result['date'] = self.timestamp
        
        # Always include timestamp field for MongoDB Atlas compatibility
        if self.timestamp:
            result['timestamp'] = self.timestamp
        elif self.date:
            result['timestamp'] = self.date
        
        # Always include unit field for MongoDB Atlas compatibility
        result['unit'] = self.unit or 'km'
        
        return result
