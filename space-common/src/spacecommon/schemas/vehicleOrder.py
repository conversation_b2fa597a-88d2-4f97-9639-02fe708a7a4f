"""VehicleOrder model for vehicle order information."""
from typing import Optional
from pydantic import BaseModel, Field, ConfigDict


class VehicleOrder(BaseModel):
    """Embedded document for vehicle order details."""
    
    model_config = ConfigDict(populate_by_name=True)
    
    mopId: Optional[str] = Field(None, description="MOP identifier")
    orderFormId: Optional[str] = Field(None, description="Order form identifier")
    orderFormStatus: Optional[str] = Field(None, description="Status of the order form")
    trackingStatus: Optional[str] = Field(None, description="Tracking status of the order")
    isUpdated: Optional[bool] = Field(None, description="Flag indicating if order has been updated")
    
    @classmethod
    def fromDict(cls, data: dict) -> 'VehicleOrder':
        """Create VehicleOrder from dictionary data."""
        return cls(
            mopId=data.get('mopId'),
            orderFormId=data.get('orderFormId'),
            orderFormStatus=data.get('orderFormStatus'),
            trackingStatus=data.get('trackingStatus'),
            isUpdated=data.get('isUpdated')
        )
    
    def toDict(self) -> dict:
        """Convert VehicleOrder to dictionary."""
        return {
            'mopId': self.mopId,
            'orderFormId': self.orderFormId,
            'orderFormStatus': self.orderFormStatus,
            'trackingStatus': self.trackingStatus,
            'isUpdated': self.isUpdated
        }