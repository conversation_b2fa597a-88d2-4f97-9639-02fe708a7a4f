"""Vehicle model for vehicle information."""
from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, ConfigDict

from .mileageData import MileageData
from .vehicleOrder import VehicleOrder


class Vehicle(BaseModel):
   
    documentId: Optional[str] = Field(None, description="MongoDB document ID")
    vin: Optional[str] = Field(None, description="Vehicle Identification Number")
    label: Optional[str] = Field(None, description="Vehicle label/description")
    brand: Optional[str] = Field(None, description="Vehicle brand")
    model: Optional[str] = Field(None, description="Vehicle model")
    modelDescription: Optional[str] = Field(None, description="Detailed model description")
    version: Optional[str] = Field(None, description="Vehicle version")
    versionId: Optional[str] = Field(None, description="Version identifier")
    registrationNumber: Optional[str] = Field(None, description="Registration number")
    registrationDate: Optional[datetime] = Field(None, description="Registration date")
    nickName: Optional[str] = Field(None, description="User-given nickname for vehicle")
    picture: Optional[str] = Field(None, description="Vehicle picture URL")
    color: Optional[str] = Field(None, description="Vehicle color")
    energy: Optional[str] = Field(None, description="Energy type (petrol, diesel, electric, etc.)")
    type: Optional[str] = Field(None, description="Vehicle type")
    sdp: Optional[str] = Field(None, description="SDP status")
    shortLabel: Optional[str] = Field(None, description="Short label for vehicle")
    lastUpdate: Optional[int] = Field(None, description="Last update timestamp")
    year: Optional[str] = Field(None, description="Manufacturing year")
    country: Optional[str] = Field(None, description="Country of origin")
    market: Optional[str] = Field(None, description="Market designation")
    regTimeStamp: Optional[int] = Field(None, description="Registration timestamp")
    warrantyStartDate: Optional[str] = Field(None, description="Warranty start date")
    make: Optional[str] = Field(None, description="Vehicle make")
    subMake: Optional[str] = Field(None, description="Sub-make designation")
    enrollmentStatus: Optional[str] = Field(None, description="Enrollment status")
    connectorType: Optional[str] = Field(None, description="Connector type")
    addStatus: Optional[str] = Field(None, description="Additional status")
    isO2x: Optional[bool] = Field(None, description="O2x flag")
    plugType: Optional[list] = Field(None, description="Plug type for electric vehicles")
    featureCodeExpiry: Optional[int] = Field(None, description="Feature code expiry timestamp")
    visual: Optional[str] = Field(None, description="Visual representation")
    language: Optional[str] = Field(None, description="Language setting")
    featureCode: List[str] = Field(default_factory=list, description="Feature codes")
    preferredDealer: Optional[Dict[str, Any]] = Field(None, description="Preferred dealer information")
    isOrder: Optional[bool] = Field(None, description="Flag indicating if vehicle is an order")
    vehicleOrder: Optional[VehicleOrder] = Field(None, description="Vehicle order details")
    mileage: Optional[MileageData] = Field(None, description="Vehicle mileage data")
    
    model_config = ConfigDict(
        populate_by_name=True
    )
    
    def toDict(self) -> dict:
        """Convert Vehicle to dictionary."""
        data = {
            'documentId': self.documentId,
            'vin': self.vin,
            'label': self.label,
            'brand': self.brand,
            'model': self.model,
            'modelDescription': self.modelDescription,
            'version': self.version,
            'versionId': self.versionId,
            'registrationNumber': self.registrationNumber,
            'registrationDate': self.registrationDate.isoformat() if self.registrationDate else None,
            'nickName': self.nickName,
            'picture': self.picture,
            'color': self.color,
            'energy': self.energy,
            'type': self.type,
            'sdp': self.sdp,
            'shortLabel': self.shortLabel,
            'lastUpdate': self.lastUpdate,
            'year': self.year,
            'country': self.country,
            'market': self.market,
            'regTimeStamp': self.regTimeStamp,
            'warrantyStartDate': self.warrantyStartDate,
            'make': self.make,
            'subMake': self.subMake,
            'enrollmentStatus': self.enrollmentStatus,
            'connectorType': self.connectorType,
            'addStatus': self.addStatus,
            'isO2x': self.isO2x,
            'plugType': self.plugType,
            'featureCodeExpiry': self.featureCodeExpiry,
            'visual': self.visual,
            'language': self.language,
            'featureCode': self.featureCode,
            'preferredDealer': self.preferredDealer,
            'isOrder': self.isOrder,
            'vehicleOrder': self.vehicleOrder.toDict() if self.vehicleOrder else None,
            'mileage': self.mileage.toDict() if self.mileage else None
        }
        
        return {k: v for k, v in data.items() if v is not None}
