"""Space Common Schemas Package.

This package contains all the Pydantic BaseModel schemas for the Space middleware system.
These are used as embedded documents, DTOs, and data validation schemas.
All schemas use camelCase naming convention for consistency.
"""

from .profile import Profile
from .mileageData import MileageData
from .vehicleOrder import VehicleOrder
from .vehicle import Vehicle
from .userPsaId import UserPsaId
from .incidentReference import IncidentReference

__all__ = [
    'Profile',
    'MileageData',
    'VehicleOrder',
    'Vehicle',
    'UserPsaId',
    'IncidentReference'
]
