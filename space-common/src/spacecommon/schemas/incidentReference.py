"""IncidentReference model for incident tracking information."""
from typing import Optional
from datetime import datetime
from pydantic import BaseModel, Field, ConfigDict


class IncidentReference(BaseModel):
    """Embedded document for storing incident references in UserData."""
    
    model_config = ConfigDict(
        populate_by_name=True
    )
    
    incidentId: Optional[str] = Field(None, description="Incident identifier")
    caseId: Optional[str] = Field(None, description="Case identifier")
    email: Optional[str] = Field(None, description="Email associated with incident")
    vin: Optional[str] = Field(None, description="Vehicle VIN associated with incident")
    brand: Optional[str] = Field(None, description="Brand associated with incident")
    country: Optional[str] = Field(None, description="Country where incident occurred")
    type: Optional[str] = Field(None, description="Type of incident")
    title: Optional[str] = Field(None, description="Incident title")
    comment: Optional[str] = Field(None, description="Additional comments about the incident")
    creationDate: Optional[datetime] = Field(default_factory=datetime.now, description="Date when incident was created")
    
    def toDict(self) -> dict:
        """Convert IncidentReference to dictionary."""
        return {
            'incidentId': self.incidentId,
            'caseId': self.caseId,
            'email': self.email,
            'vin': self.vin,
            'brand': self.brand,
            'country': self.country,
            'type': self.type,
            'title': self.title,
            'comment': self.comment,
            'creationDate': self.creationDate.isoformat() if self.creationDate else None
        }
