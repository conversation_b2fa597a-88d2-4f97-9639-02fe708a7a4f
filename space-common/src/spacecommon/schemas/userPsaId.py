"""UserPsaId model for PSA identification information."""
from typing import Optional
from pydantic import BaseModel, Field, ConfigDict


class UserPsaId(BaseModel):
    """Embedded document for user PSA identification."""
    
    model_config = ConfigDict(populate_by_name=True)
    
    cvsId: Optional[str] = Field(None, description="CVS identifier")
    brand: Optional[str] = Field(None, description="Brand associated with the PSA ID")
    
    def toDict(self) -> dict:
        """Convert UserPsaId to dictionary."""
        return {
            'cvsId': self.cvsId,
            'brand': self.brand
        }
    
    @classmethod
    def fromDict(cls, data: dict) -> 'UserPsaId':
        """Create UserPsaId from dictionary data."""
        return cls(
            cvsId=data.get('cvsId'),
            brand=data.get('brand')
        )