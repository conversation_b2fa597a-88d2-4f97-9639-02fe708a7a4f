"""Profile model for user profile information."""
from typing import Optional
from pydantic import BaseModel, Field, EmailStr, ConfigDict


class Profile(BaseModel):
    """Embedded document for user profile information."""
    
    model_config = ConfigDict(
        populate_by_name=True
    )
    
    email: EmailStr = Field(..., description="User's email address")
    title: Optional[str] = Field(None, description="User's title (Mr., Mrs., etc.)")
    firstName: Optional[str] = Field(None, description="User's first name")
    lastName: Optional[str] = Field(None, description="User's last name")
    phone: Optional[str] = Field(None, description="User's phone number")
    address1: Optional[str] = Field(None, description="Primary address line")
    address2: Optional[str] = Field(None, description="Secondary address line")
    city: Optional[str] = Field(None, description="City")
    zip: Optional[str] = Field(None, description="ZIP/Postal code")
    country: Optional[str] = Field(None, description="Country")
    
    def toDict(self) -> dict:
        """Convert Profile to dictionary."""
        return {
            'email': self.email,
            'title': self.title,
            'firstName': self.firstName,
            'lastName': self.lastName,
            'phone': self.phone,
            'address1': self.address1,
            'address2': self.address2,
            'city': self.city,
            'zip': self.zip,
            'country': self.country
        }