"""Space Common Package.

This package provides shared models and schemas for the Space middleware system.

- models: Contains Beanie Document models for MongoDB integration
- schemas: Contains Pydantic BaseModel schemas for data validation and DTOs
"""

# Import models (Beanie Documents)
from .models import UserData, ConsentArbitration, SPSEligibility

# Import schemas (Pydantic BaseModels)
from .schemas import (
    Profile,
    MileageData,
    VehicleOrder,
    Vehicle,
    UserPsaId,
    IncidentReference
)

__all__ = [
    # Models
    'UserData',
    'ConsentArbitration',
    'SPSEligibility',
    # Schemas
    'Profile',
    'MileageData',
    'VehicleOrder',
    'Vehicle',
    'UserPsaId',
    'IncidentReference'
]
