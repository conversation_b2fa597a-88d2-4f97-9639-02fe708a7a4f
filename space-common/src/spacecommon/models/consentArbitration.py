"""ConsentArbitration model - document for storing user consent arbitration data."""
from typing import Optional
from beanie import Document, Indexed
from pydantic import Field

class ConsentArbitration(Document):
    """Document for storing user consent arbitration information."""
    
    userId: Indexed(str) = Field(..., description="User identifier")
    vin: str = Field(..., description="Vehicle identification number")
    consent: bool = Field(..., description="User consent status")
    brand: str = Field(..., description="Vehicle brand")
    country: str = Field(..., description="Country code")
    source: str = Field(..., description="Source of the consent")
    region: str = Field(..., description="Region identifier")
    exported: bool = Field(..., description="Export status")
    createdAt: int = Field(..., description="Creation timestamp")
    warrantyDocumentLink: Optional[str] = Field(
        None, 
        description="Link to warranty document"
    )
    
    class Settings:
        """Beanie document settings."""
        name = "consentArbitration"  # MongoDB collection name
        use_state_management = True
        validate_on_save = True
    
    def toDict(self) -> dict:
        """Convert ConsentArbitration to dictionary."""
        data = {
            '_id': str(self.id) if self.id else None,
            'userId': self.userId,
            'vin': self.vin,
            'consent': self.consent,
            'brand': self.brand,
            'country': self.country,
            'source': self.source,
            'region': self.region,
            'exported': self.exported,
            'createdAt': self.createdAt,
            'warrantyDocumentLink': self.warrantyDocumentLink
        }
        
        return {k: v for k, v in data.items() if v is not None}
    
    @classmethod
    def fromDict(cls, data: dict) -> 'ConsentArbitration':
        """Create ConsentArbitration instance from dictionary."""
        doc_id = data.pop('_id', None)
        
        consent_arbitration = cls(
            userId=data.get('userId'),
            vin=data.get('vin'),
            consent=data.get('consent'),
            brand=data.get('brand'),
            country=data.get('country'),
            source=data.get('source'),
            region=data.get('region'),
            exported=data.get('exported'),
            createdAt=data.get('createdAt'),
            warrantyDocumentLink=data.get('warrantyDocumentLink')
        )
        
        if doc_id:
            consent_arbitration.id = doc_id
        
        return consent_arbitration
