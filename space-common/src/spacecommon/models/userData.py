"""UserData model - main document for user context information."""
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
from beanie import Document, Indexed
from pydantic import Field

from ..schemas.vehicle import Vehicle
from ..schemas.userPsaId import UserPsaId
from ..schemas.incidentReference import IncidentReference
from ..schemas.profile import Profile

class UserData(Document):
    """Main document for storing user data and context."""
    userId: Optional[Indexed(str)] = Field(None, description="User identifier")
    userDbId: Optional[str] = Field(None, description="User database identifier")
    vehicle: List[Vehicle] = Field(default_factory=list, description="List of user vehicles")
    userPsaId: List[UserPsaId] = Field(default_factory=list, description="List of user PSA IDs")
    incidents: Optional[List[IncidentReference]] = Field(default_factory=list, description="List of incident references")
    preferredDealer: Optional[Dict[str, Any]] = Field(None, description="Preferred dealer information by brand")
    profile: Optional[Profile] = Field(None, description="User profile information")
    
    class Settings:
        """Beanie document settings."""
        name = "userData"  # MongoDB collection name
        use_state_management = True
        validate_on_save = True
    
    
    def toDict(self) -> dict:
        """Convert UserData to dictionary."""
        data = {
            '_id': str(self.id) if self.id else None,
            'userId': self.userId,
            'userDbId': self.userDbId,
            'vehicle': [v.toDict() for v in self.vehicle],
            'userPsaId': [p.toDict() for p in self.userPsaId] if self.userPsaId else None,
            'incidents': [i.toDict() for i in self.incidents] if self.incidents else None,
            'preferredDealer': self.preferredDealer,
            'profile': self.profile.toDict() if self.profile else None
        }
        
        return {k: v for k, v in data.items() if v is not None}
    
    @classmethod
    def fromDict(cls, data: dict) -> 'UserData':
        doc_id = data.pop('_id', None)
        
        userData = cls(
            userId=data.get('userId'),
            userDbId=data.get('userDbId'),
            preferredDealer=data.get('preferredDealer')
        )
        
        if doc_id:
            userData.id = doc_id
        
        if 'vehicle' in data and data['vehicle']:
            userData.vehicle = [Vehicle(**v) for v in data['vehicle']]
        
        if 'userPsaId' in data and data['userPsaId']:
            userData.userPsaId = [UserPsaId(**p) for p in data['userPsaId']]
        
        if 'incidents' in data and data['incidents']:
            userData.incidents = [IncidentReference(**i) for i in data['incidents']]
        
        if 'profile' in data and data['profile']:
            userData.profile = Profile(**data['profile'])
        
        return userData