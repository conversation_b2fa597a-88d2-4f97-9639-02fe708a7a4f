"""Space Common Models Package.

This package contains Beanie Document models for MongoDB integration.
All models use camelCase naming convention for consistency.

For Pydantic BaseModel schemas, see the schemas package.
"""

from .userData import UserData
from .consentArbitration import ConsentArbitration
from .spsEligibility import SPSEligibility

__all__ = [
    'UserData',
    'ConsentArbitration',
    'SPSEligibility'
]
