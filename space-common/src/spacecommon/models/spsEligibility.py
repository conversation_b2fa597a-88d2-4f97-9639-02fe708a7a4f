"""SPSEligibility model - document for storing SPS eligibility data."""
from typing import Optional, List, Any
from beanie import Document
from pydantic import Field


class SPSEligibility(Document):
    """Document for storing SPS eligibility information."""
    
    scope: Optional[str] = Field(None, description="Scope of eligibility")
    codes: List[Any] = Field(default_factory=list, description="Collection of codes")
    eligibilityRule: Optional[str] = Field(None, description="Eligibility rule")
    type: Optional[str] = Field(None, description="Type of eligibility")
    eligibilityDisclaimer: Optional[str] = Field(None, description="Eligibility disclaimer")
    name: Optional[str] = Field(None, description="Name")
    description: Optional[str] = Field(None, description="Description")
    active: Optional[bool] = Field(None, description="Active status")
    
    class Settings:
        """Beanie document settings."""
        name = "boSPSEligibility"  # MongoDB collection name
        use_state_management = True
        validate_on_save = True
    
    def toDict(self) -> dict:
        """Convert SPSEligibility to dictionary."""
        data = {
            '_id': str(self.id) if self.id else None,
            'scope': self.scope,
            'codes': self.codes,
            'eligibilityRule': self.eligibilityRule,
            'type': self.type,
            'eligibilityDisclaimer': self.eligibilityDisclaimer,
            'name': self.name,
            'description': self.description,
            'active': self.active
        }
        
        return {k: v for k, v in data.items() if v is not None}
    
    @classmethod
    def fromDict(cls, data: dict) -> 'SPSEligibility':
        """Create SPSEligibility instance from dictionary."""
        doc_id = data.pop('_id', None)
        
        sps_eligibility = cls(
            scope=data.get('scope'),
            codes=data.get('codes', []),
            eligibilityRule=data.get('eligibilityRule'),
            type=data.get('type'),
            eligibilityDisclaimer=data.get('eligibilityDisclaimer'),
            name=data.get('name'),
            description=data.get('description'),
            active=data.get('active')
        )
        
        if doc_id:
            sps_eligibility.id = doc_id
        
        return sps_eligibility