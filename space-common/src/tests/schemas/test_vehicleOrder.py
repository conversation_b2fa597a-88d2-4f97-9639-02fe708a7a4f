"""Test cases for VehicleOrder Pydantic BaseModel schema."""
import pytest
from pydantic import ValidationError

from spacecommon.schemas.vehicleOrder import VehicleOrder
from tests.testData.schemasData import (
    validVehicleOrderPayloadData,
    invalidVehicleOrderPayloadData,
    expectedVehicleOrderDictOutput
)


class TestVehicleOrderSchema:
    """Test class for VehicleOrder Pydantic BaseModel schema."""

    def testValidVehicleOrderCreation(self, validVehicleOrderPayload):
        """Test creating a valid VehicleOrder instance."""
        vehicleOrder = VehicleOrder(**validVehicleOrderPayload)
        assert vehicleOrder.mopId == "mop_12345"
        assert vehicleOrder.orderFormId == "order_67890"
        assert vehicleOrder.orderFormStatus == "pending"
        assert vehicleOrder.trackingStatus == "processing"
        assert vehicleOrder.isUpdated is True

    def testInvalidVehicleOrderCreation(self, invalidVehicleOrderPayload):
        """Test creating VehicleOrder with invalid data should raise ValidationError."""
        with pytest.raises(ValidationError):
            VehicleOrder(**invalidVehicleOrderPayload)

    def testVehicleOrderWithNoneValues(self):
        """Test VehicleOrder with None values for all optional fields."""
        vehicleOrder = VehicleOrder()
        assert vehicleOrder.mopId is None
        assert vehicleOrder.orderFormId is None
        assert vehicleOrder.orderFormStatus is None
        assert vehicleOrder.trackingStatus is None
        assert vehicleOrder.isUpdated is None

    def testVehicleOrderToDictMethod(self, validVehicleOrderPayload):
        """Test VehicleOrder toDict() method."""
        vehicleOrder = VehicleOrder(**validVehicleOrderPayload)
        vehicleOrderDict = vehicleOrder.toDict()
        
        assert vehicleOrderDict["mopId"] == "mop_12345"
        assert vehicleOrderDict["orderFormId"] == "order_67890"
        assert vehicleOrderDict["orderFormStatus"] == "pending"
        assert vehicleOrderDict["trackingStatus"] == "processing"
        assert vehicleOrderDict["isUpdated"] is True

    def testVehicleOrderFromDictMethod(self, validVehicleOrderPayload):
        """Test VehicleOrder fromDict() class method."""
        vehicleOrder = VehicleOrder.fromDict(validVehicleOrderPayload)
        
        assert vehicleOrder.mopId == "mop_12345"
        assert vehicleOrder.orderFormId == "order_67890"
        assert vehicleOrder.orderFormStatus == "pending"
        assert vehicleOrder.trackingStatus == "processing"
        assert vehicleOrder.isUpdated is True

    def testVehicleOrderSerialization(self, validVehicleOrderPayload):
        """Test VehicleOrder JSON serialization."""
        vehicleOrder = VehicleOrder(**validVehicleOrderPayload)
        
        vehicleOrderDict = vehicleOrder.model_dump()
        assert vehicleOrderDict["mopId"] == "mop_12345"
        
        vehicleOrderJson = vehicleOrder.model_dump_json()
        assert isinstance(vehicleOrderJson, str)
        assert "mop_12345" in vehicleOrderJson

    def testVehicleOrderBooleanValidation(self):
        """Test VehicleOrder boolean field validation."""
        # Valid boolean values
        vehicleOrder1 = VehicleOrder(isUpdated=True)
        assert vehicleOrder1.isUpdated is True
        
        vehicleOrder2 = VehicleOrder(isUpdated=False)
        assert vehicleOrder2.isUpdated is False
        
        # Invalid boolean values should raise ValidationError
        with pytest.raises(ValidationError):
            VehicleOrder(isUpdated="not_boolean")

    def testVehicleOrderConfigurationSettings(self):
        """Test VehicleOrder Pydantic configuration settings."""
        config = VehicleOrder.model_config
        assert config.get('populate_by_name') is True
