"""Test cases for MileageData Pydantic BaseModel schema."""
import pytest
from pydantic import ValidationError

from spacecommon.schemas.mileageData import MileageData
from tests.testData.schemasData import (
    validMileageDataPayloadData,
    invalidMileageDataPayloadData,
    expectedMileageDataDictOutput
)


class TestMileageDataSchema:
    """Test class for MileageData Pydantic BaseModel schema."""

    def testValidMileageDataCreation(self, validMileageDataPayload):
        """Test creating a valid MileageData instance."""
        mileageData = MileageData(**validMileageDataPayload)
        assert mileageData.value == 25000
        assert mileageData.date == 1640995200
        assert mileageData.timestamp == 1640995200
        assert mileageData.unit == "km"

    def testInvalidMileageDataCreation(self, invalidMileageDataPayload):
        """Test creating MileageData with invalid data should raise ValidationError."""
        with pytest.raises(ValidationError):
            MileageData(**invalidMileageDataPayload)

    def testMileageDataWithDefaultUnit(self):
        """Test MileageData with default unit value."""
        mileageData = MileageData(value=15000)
        assert mileageData.value == 15000
        assert mileageData.unit == "km"  # Default value
        assert mileageData.date is None
        assert mileageData.timestamp is None

    def testMileageDataWithMilesUnit(self):
        """Test MileageData with miles unit."""
        mileageData = MileageData(
            value=15000,
            date=1640995200,
            unit="miles"
        )
        assert mileageData.value == 15000
        assert mileageData.unit == "miles"
        assert mileageData.date == 1640995200

    def testMileageDataToDictMethod(self, validMileageDataPayload):
        """Test MileageData toDict() method."""
        mileageData = MileageData(**validMileageDataPayload)
        mileageDataDict = mileageData.toDict()
        
        assert mileageDataDict["value"] == 25000
        assert mileageDataDict["date"] == 1640995200
        assert mileageDataDict["timestamp"] == 1640995200
        assert mileageDataDict["unit"] == "km"

    def testMileageDataToDictWithOnlyDate(self):
        """Test MileageData toDict() method with only date field."""
        mileageData = MileageData(
            value=10000,
            date=1640995200,
            unit="km"
        )
        mileageDataDict = mileageData.toDict()
        
        assert mileageDataDict["value"] == 10000
        assert mileageDataDict["date"] == 1640995200
        assert mileageDataDict["timestamp"] == 1640995200  # Should copy from date
        assert mileageDataDict["unit"] == "km"

    def testMileageDataToDictWithOnlyTimestamp(self):
        """Test MileageData toDict() method with only timestamp field."""
        mileageData = MileageData(
            value=10000,
            timestamp=1640995200,
            unit="km"
        )
        mileageDataDict = mileageData.toDict()
        
        assert mileageDataDict["value"] == 10000
        assert mileageDataDict["date"] == 1640995200  # Should copy from timestamp
        assert mileageDataDict["timestamp"] == 1640995200
        assert mileageDataDict["unit"] == "km"

    def testMileageDataToDictWithoutDateOrTimestamp(self):
        """Test MileageData toDict() method without date or timestamp."""
        mileageData = MileageData(
            value=10000,
            unit="miles"
        )
        mileageDataDict = mileageData.toDict()
        
        assert mileageDataDict["value"] == 10000
        assert mileageDataDict["unit"] == "miles"
        # date and timestamp should not be in dict if both are None
        assert "date" not in mileageDataDict or mileageDataDict["date"] is None
        assert "timestamp" not in mileageDataDict or mileageDataDict["timestamp"] is None

    def testMileageDataUnitDefaultValue(self):
        """Test MileageData unit field default value handling."""
        mileageData = MileageData(value=5000)
        assert mileageData.unit == "km"
        
        # Test toDict preserves default unit
        mileageDataDict = mileageData.toDict()
        assert mileageDataDict["unit"] == "km"

    def testMileageDataWithNoneValues(self):
        """Test MileageData with None values for optional fields."""
        mileageData = MileageData(
            value=None,
            date=None,
            timestamp=None,
            unit="km"
        )
        
        assert mileageData.value is None
        assert mileageData.date is None
        assert mileageData.timestamp is None
        assert mileageData.unit == "km"

    def testMileageDataSerialization(self, validMileageDataPayload):
        """Test MileageData JSON serialization."""
        mileageData = MileageData(**validMileageDataPayload)
        
        # Test model_dump
        mileageDataDict = mileageData.model_dump()
        assert mileageDataDict["value"] == 25000
        assert mileageDataDict["unit"] == "km"
        
        # Test model_dump_json
        mileageDataJson = mileageData.model_dump_json()
        assert isinstance(mileageDataJson, str)
        assert "25000" in mileageDataJson

    def testMileageDataDeserialization(self, validMileageDataPayload):
        """Test MileageData deserialization from dictionary."""
        mileageData = MileageData(**validMileageDataPayload)
        mileageDataDict = mileageData.model_dump()
        
        # Create new MileageData from dictionary
        newMileageData = MileageData(**mileageDataDict)
        assert newMileageData.value == mileageData.value
        assert newMileageData.date == mileageData.date
        assert newMileageData.timestamp == mileageData.timestamp
        assert newMileageData.unit == mileageData.unit

    def testMileageDataValidation(self):
        """Test MileageData field validation."""
        # Valid integer values
        mileageData = MileageData(value=0)
        assert mileageData.value == 0
        
        mileageData = MileageData(value=999999)
        assert mileageData.value == 999999
        
        # Test negative values (should be allowed if no validator prevents it)
        mileageData = MileageData(value=-1)
        assert mileageData.value == -1

    def testMileageDataConfigurationSettings(self):
        """Test MileageData Pydantic configuration settings."""
        config = MileageData.model_config
        assert config.get('populate_by_name') is True

    def testMileageDataFieldTypes(self):
        """Test MileageData field type validation."""
        # Test that string values for numeric fields raise ValidationError
        with pytest.raises(ValidationError):
            MileageData(value="not_a_number")
        
        with pytest.raises(ValidationError):
            MileageData(date="not_a_timestamp")
        
        with pytest.raises(ValidationError):
            MileageData(timestamp="not_a_timestamp")

    def testMileageDataUpdate(self, validMileageDataPayload):
        """Test MileageData field updates."""
        mileageData = MileageData(**validMileageDataPayload)
        
        # Update fields
        originalValue = mileageData.value
        mileageData.value = 30000
        mileageData.unit = "miles"
        
        assert mileageData.value == 30000
        assert mileageData.unit == "miles"
        assert originalValue != mileageData.value

    def testMileageDataComparison(self, validMileageDataPayload):
        """Test MileageData instance comparison."""
        mileageData1 = MileageData(**validMileageDataPayload)
        mileageData2 = MileageData(**validMileageDataPayload)
        
        # Should be equal (same data)
        assert mileageData1.model_dump() == mileageData2.model_dump()
        
        # Modify one instance
        mileageData2.value = 50000
        assert mileageData1.model_dump() != mileageData2.model_dump()

    def testMileageDataEdgeCases(self):
        """Test MileageData edge cases."""
        # Test with zero values
        mileageData = MileageData(
            value=0,
            date=0,
            timestamp=0,
            unit=""
        )
        assert mileageData.value == 0
        assert mileageData.date == 0
        assert mileageData.timestamp == 0
        assert mileageData.unit == ""

    def testMileageDataToDictLogic(self):
        """Test specific MileageData toDict() logic for date/timestamp handling."""
        # Test when both date and timestamp are provided
        mileageData = MileageData(
            value=15000,
            date=1640995200,
            timestamp=1640995300,  # Different from date
            unit="km"
        )
        mileageDataDict = mileageData.toDict()
        
        # Both should be preserved in toDict
        assert mileageDataDict["date"] == 1640995200
        assert mileageDataDict["timestamp"] == 1640995300
