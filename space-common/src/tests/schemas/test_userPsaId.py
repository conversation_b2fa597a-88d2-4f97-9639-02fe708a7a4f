"""Test cases for UserPsaId Pydantic BaseModel schema."""
import pytest
from pydantic import ValidationError

from spacecommon.schemas.userPsaId import UserPsaId
from tests.testData.schemasData import (
    validUserPsaIdPayloadData,
    invalidUserPsaIdPayloadData,
    expectedUserPsaIdDictOutput
)


class TestUserPsaIdSchema:
    """Test class for UserPsaId Pydantic BaseModel schema."""

    def testValidUserPsaIdCreation(self, validUserPsaIdPayload):
        """Test creating a valid UserPsaId instance."""
        userPsaId = UserPsaId(**validUserPsaIdPayload)
        assert userPsaId.cvsId == "cvs_123456"
        assert userPsaId.brand == "Honda"

    def testUserPsaIdWithNoneValues(self):
        """Test UserPsaId with None values for optional fields."""
        userPsaId = UserPsaId()
        assert userPsaId.cvsId is None
        assert userPsaId.brand is None

    def testUserPsaIdToDictMethod(self, validUserPsaIdPayload):
        """Test UserPsaId toDict() method."""
        userPsaId = UserPsaId(**validUserPsaIdPayload)
        userPsaIdDict = userPsaId.toDict()
        
        assert userPsaIdDict["cvsId"] == "cvs_123456"
        assert userPsaIdDict["brand"] == "Honda"

    def testUserPsaIdFromDictMethod(self, validUserPsaIdPayload):
        """Test UserPsaId fromDict() class method."""
        userPsaId = UserPsaId.fromDict(validUserPsaIdPayload)
        
        assert userPsaId.cvsId == "cvs_123456"
        assert userPsaId.brand == "Honda"

    def testUserPsaIdSerialization(self, validUserPsaIdPayload):
        """Test UserPsaId JSON serialization."""
        userPsaId = UserPsaId(**validUserPsaIdPayload)
        
        userPsaIdDict = userPsaId.model_dump()
        assert userPsaIdDict["cvsId"] == "cvs_123456"
        
        userPsaIdJson = userPsaId.model_dump_json()
        assert isinstance(userPsaIdJson, str)
        assert "cvs_123456" in userPsaIdJson

    def testUserPsaIdWithEmptyStrings(self):
        """Test UserPsaId with empty strings."""
        userPsaId = UserPsaId(cvsId="", brand="")
        assert userPsaId.cvsId == ""
        assert userPsaId.brand == ""

    def testUserPsaIdConfigurationSettings(self):
        """Test UserPsaId Pydantic configuration settings."""
        config = UserPsaId.model_config
        assert config.get('populate_by_name') is True
