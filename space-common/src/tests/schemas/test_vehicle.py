"""Test cases for Vehicle Pydantic BaseModel schema."""
import pytest
from datetime import datetime
from pydantic import ValidationError

from spacecommon.schemas.vehicle import Vehicle
from spacecommon.schemas.mileageData import MileageData
from spacecommon.schemas.vehicleOrder import VehicleOrder
from tests.testData.schemasData import (
    validVehiclePayloadData,
    invalidVehiclePayloadData
)


class TestVehicleSchema:
    """Test class for Vehicle Pydantic BaseModel schema."""

    def testValidVehicleCreation(self, validVehiclePayload):
        """Test creating a valid Vehicle instance."""
        vehicle = Vehicle(**validVehiclePayload)
        assert vehicle.documentId == "doc_123456"
        assert vehicle.vin == "1HGBH41JXMN109186"
        assert vehicle.label == "My Honda Civic"
        assert vehicle.brand == "Honda"
        assert vehicle.model == "Civic"
        assert vehicle.modelDescription == "Honda Civic LX 4-Door Sedan"
        assert vehicle.version == "LX"
        assert vehicle.versionId == "v_001"
        assert vehicle.registrationNumber == "ABC123"
        assert isinstance(vehicle.registrationDate, datetime)
        assert vehicle.nickName == "Blue Beast"
        assert vehicle.picture == "https://example.com/car.jpg"
        assert vehicle.color == "Blue"
        assert vehicle.energy == "Gasoline"
        assert vehicle.type == "Sedan"
        assert vehicle.year == "2021"
        assert vehicle.isO2x is False
        assert isinstance(vehicle.plugType, list)
        assert isinstance(vehicle.featureCode, list)
        assert isinstance(vehicle.vehicleOrder, VehicleOrder)
        assert isinstance(vehicle.mileage, MileageData)

    def testInvalidVehicleCreation(self, invalidVehiclePayload):
        """Test creating Vehicle with invalid data should raise ValidationError."""
        with pytest.raises(ValidationError):
            Vehicle(**invalidVehiclePayload)

    def testVehicleWithMinimalData(self):
        """Test Vehicle with minimal data (all fields optional)."""
        vehicle = Vehicle()
        assert vehicle.documentId is None
        assert vehicle.vin is None
        assert vehicle.label is None
        assert vehicle.brand is None
        assert vehicle.model is None
        assert vehicle.featureCode == []  # Default factory list
        assert vehicle.plugType is None
        assert vehicle.vehicleOrder is None
        assert vehicle.mileage is None

    def testVehicleWithEmbeddedSchemas(self):
        """Test Vehicle with embedded MileageData and VehicleOrder."""
        mileageData = {
            "value": 15000,
            "date": 1640995200,
            "unit": "km"
        }
        
        vehicleOrderData = {
            "mopId": "mop_123",
            "orderFormId": "order_456",
            "orderFormStatus": "completed",
            "isUpdated": True
        }
        
        vehicle = Vehicle(
            vin="TEST123456789",
            brand="TestBrand",
            mileage=mileageData,
            vehicleOrder=vehicleOrderData
        )
        
        assert vehicle.vin == "TEST123456789"
        assert isinstance(vehicle.mileage, MileageData)
        assert vehicle.mileage.value == 15000
        assert isinstance(vehicle.vehicleOrder, VehicleOrder)
        assert vehicle.vehicleOrder.mopId == "mop_123"

    def testVehicleToDictMethod(self, validVehiclePayload):
        """Test Vehicle toDict() method."""
        vehicle = Vehicle(**validVehiclePayload)
        vehicleDict = vehicle.toDict()
        
        assert vehicleDict["documentId"] == "doc_123456"
        assert vehicleDict["vin"] == "1HGBH41JXMN109186"
        assert vehicleDict["brand"] == "Honda"
        assert vehicleDict["model"] == "Civic"
        assert isinstance(vehicleDict["registrationDate"], str)  # ISO format
        assert isinstance(vehicleDict["featureCode"], list)
        assert isinstance(vehicleDict["vehicleOrder"], dict)
        assert isinstance(vehicleDict["mileage"], dict)

    def testVehicleToDictWithNoneValues(self):
        """Test Vehicle toDict() method with None values."""
        vehicle = Vehicle(
            vin="TEST123",
            brand="TestBrand",
            mileage=None,
            vehicleOrder=None
        )
        vehicleDict = vehicle.toDict()
        
        assert vehicleDict["vin"] == "TEST123"
        assert vehicleDict["brand"] == "TestBrand"
        # None values should be filtered out
        assert "mileage" not in vehicleDict or vehicleDict["mileage"] is None
        assert "vehicleOrder" not in vehicleDict or vehicleDict["vehicleOrder"] is None

    def testVehicleDatetimeHandling(self):
        """Test Vehicle datetime field handling."""
        testDatetime = datetime(2021, 1, 15, 10, 0, 0)
        vehicle = Vehicle(
            vin="TEST123",
            registrationDate=testDatetime
        )
        
        assert vehicle.registrationDate == testDatetime
        
        # Test toDict() formats datetime as ISO string
        vehicleDict = vehicle.toDict()
        assert vehicleDict["registrationDate"] == testDatetime.isoformat()

    def testVehicleListFields(self):
        """Test Vehicle list field handling."""
        vehicle = Vehicle(
            vin="TEST123",
            featureCode=["FC001", "FC002", "FC003"],
            plugType=["type1", "type2"]
        )
        
        assert vehicle.featureCode == ["FC001", "FC002", "FC003"]
        assert vehicle.plugType == ["type1", "type2"]
        
        # Test toDict preserves lists
        vehicleDict = vehicle.toDict()
        assert vehicleDict["featureCode"] == ["FC001", "FC002", "FC003"]
        assert vehicleDict["plugType"] == ["type1", "type2"]

    def testVehicleBooleanFields(self):
        """Test Vehicle boolean field validation."""
        vehicle = Vehicle(
            vin="TEST123",
            isO2x=True,
            isOrder=False
        )
        
        assert vehicle.isO2x is True
        assert vehicle.isOrder is False
        
        # Invalid boolean values should raise ValidationError
        with pytest.raises(ValidationError):
            Vehicle(isO2x="not_boolean")
        
        with pytest.raises(ValidationError):
            Vehicle(isOrder="not_boolean")

    def testVehicleIntegerFields(self):
        """Test Vehicle integer field validation."""
        vehicle = Vehicle(
            vin="TEST123",
            lastUpdate=1640995200,
            regTimeStamp=1610668800,
            featureCodeExpiry=1672531200
        )
        
        assert vehicle.lastUpdate == 1640995200
        assert vehicle.regTimeStamp == 1610668800
        assert vehicle.featureCodeExpiry == 1672531200
        
        # Invalid integer values should raise ValidationError
        with pytest.raises(ValidationError):
            Vehicle(lastUpdate="not_integer")

    def testVehicleSerialization(self, validVehiclePayload):
        """Test Vehicle JSON serialization."""
        vehicle = Vehicle(**validVehiclePayload)
        
        # Test model_dump
        vehicleDict = vehicle.model_dump()
        assert vehicleDict["vin"] == "1HGBH41JXMN109186"
        assert isinstance(vehicleDict["registrationDate"], datetime)
        
        # Test model_dump with mode='json' for serialization
        vehicleJsonDict = vehicle.model_dump(mode='json')
        assert isinstance(vehicleJsonDict["registrationDate"], str)
        
        # Test model_dump_json
        vehicleJson = vehicle.model_dump_json()
        assert isinstance(vehicleJson, str)
        assert "1HGBH41JXMN109186" in vehicleJson

    def testVehicleDeserialization(self, validVehiclePayload):
        """Test Vehicle deserialization from dictionary."""
        vehicle = Vehicle(**validVehiclePayload)
        vehicleDict = vehicle.model_dump()
        
        # Create new Vehicle from dictionary
        newVehicle = Vehicle(**vehicleDict)
        assert newVehicle.vin == vehicle.vin
        assert newVehicle.brand == vehicle.brand
        assert newVehicle.model == vehicle.model

    def testVehicleConfigurationSettings(self):
        """Test Vehicle Pydantic configuration settings."""
        config = Vehicle.model_config
        assert config.get('populate_by_name') is True
        
        # Check json_encoders for datetime
        jsonEncoders = config.get('json_encoders', {})
        assert datetime in jsonEncoders or len(jsonEncoders) >= 0

    def testVehicleComplexFieldTypes(self):
        """Test Vehicle complex field type validation."""
        # Test preferredDealer as dict
        preferredDealer = {
            "dealerId": "dealer_001",
            "name": "Test Dealer",
            "address": "123 Test St"
        }
        
        vehicle = Vehicle(
            vin="TEST123",
            preferredDealer=preferredDealer
        )
        
        assert vehicle.preferredDealer == preferredDealer
        assert isinstance(vehicle.preferredDealer, dict)

    def testVehicleUpdate(self, validVehiclePayload):
        """Test Vehicle field updates."""
        vehicle = Vehicle(**validVehiclePayload)
        
        # Update fields
        originalVin = vehicle.vin
        vehicle.vin = "UPDATED123456789"
        vehicle.nickName = "Updated Nickname"
        vehicle.isO2x = True
        
        assert vehicle.vin == "UPDATED123456789"
        assert vehicle.nickName == "Updated Nickname"
        assert vehicle.isO2x is True
        assert originalVin != vehicle.vin

    def testVehicleComparison(self, validVehiclePayload):
        """Test Vehicle instance comparison."""
        vehicle1 = Vehicle(**validVehiclePayload)
        vehicle2 = Vehicle(**validVehiclePayload)
        
        # Should be equal (same data)
        assert vehicle1.model_dump() == vehicle2.model_dump()
        
        # Modify one instance
        vehicle2.vin = "DIFFERENT123456789"
        assert vehicle1.model_dump() != vehicle2.model_dump()

    def testVehicleEmbeddedSchemaValidation(self):
        """Test Vehicle embedded schema validation."""
        # Invalid mileage data should raise ValidationError
        with pytest.raises(ValidationError):
            Vehicle(
                vin="TEST123",
                mileage={"value": "not_a_number"}  # Invalid mileage data
            )
        
        # Invalid vehicle order data should raise ValidationError
        with pytest.raises(ValidationError):
            Vehicle(
                vin="TEST123",
                vehicleOrder={"isUpdated": "not_boolean"}  # Invalid vehicle order data
            )

    def testVehicleFeatureCodeDefaultFactory(self):
        """Test Vehicle featureCode default factory behavior."""
        vehicle1 = Vehicle(vin="TEST1")
        vehicle2 = Vehicle(vin="TEST2")
        
        # Default factory should create separate lists
        assert vehicle1.featureCode == []
        assert vehicle2.featureCode == []
        assert vehicle1.featureCode is not vehicle2.featureCode  # Different instances

    def testVehicleEdgeCases(self):
        """Test Vehicle edge cases."""
        # Test with empty strings
        vehicle = Vehicle(
            documentId="",
            vin="",
            label="",
            brand="",
            model=""
        )
        
        assert vehicle.documentId == ""
        assert vehicle.vin == ""
        assert vehicle.label == ""

    def testVehicleWithAllFieldTypes(self):
        """Test Vehicle with all different field types."""
        vehicle = Vehicle(
            documentId="doc_123",  # Optional[str]
            vin="VIN123456789",     # Optional[str]
            registrationDate=datetime.now(),  # Optional[datetime]
            lastUpdate=1640995200,  # Optional[int]
            isO2x=True,            # Optional[bool]
            plugType=["type1"],    # Optional[list]
            featureCode=["FC001"], # List[str] with default_factory
            preferredDealer={"id": "dealer1"},  # Optional[Dict[str, Any]]
            vehicleOrder=VehicleOrder(mopId="mop1"),  # Optional[VehicleOrder]
            mileage=MileageData(value=1000)  # Optional[MileageData]
        )
        
        assert isinstance(vehicle.documentId, str)
        assert isinstance(vehicle.vin, str)
        assert isinstance(vehicle.registrationDate, datetime)
        assert isinstance(vehicle.lastUpdate, int)
        assert isinstance(vehicle.isO2x, bool)
        assert isinstance(vehicle.plugType, list)
        assert isinstance(vehicle.featureCode, list)
        assert isinstance(vehicle.preferredDealer, dict)
        assert isinstance(vehicle.vehicleOrder, VehicleOrder)
        assert isinstance(vehicle.mileage, MileageData)
