"""Test cases for Profile Pydantic BaseModel schema."""
import pytest
from pydantic import ValidationError

from spacecommon.schemas.profile import Profile
from tests.testData.schemasData import (
    validProfilePayloadData,
    invalidProfilePayloadData,
    expectedProfileDictOutput
)


class TestProfileSchema:
    """Test class for Profile Pydantic BaseModel schema."""

    def testValidProfileCreation(self, validProfilePayload):
        """Test creating a valid Profile instance."""
        profile = Profile(**validProfilePayload)
        assert profile.email == "<EMAIL>"
        assert profile.title == "Mr."
        assert profile.firstName == "John"
        assert profile.lastName == "Doe"
        assert profile.phone == "+1234567890"
        assert profile.address1 == "123 Main Street"
        assert profile.address2 == "Apt 4B"
        assert profile.city == "New York"
        assert profile.zip == "10001"
        assert profile.country == "US"

    def testInvalidProfileCreation(self, invalidProfilePayload):
        """Test creating Profile with invalid email should raise ValidationError."""
        with pytest.raises(ValidationError) as excInfo:
            Profile(**invalidProfilePayload)
        
        # Check that email validation failed
        errors = excInfo.value.errors()
        emailErrors = [error for error in errors if error['loc'] == ('email',)]
        assert len(emailErrors) > 0

    def testProfileWithRequiredEmailOnly(self):
        """Test Profile with only required email field."""
        profile = Profile(email="<EMAIL>")
        assert profile.email == "<EMAIL>"
        assert profile.title is None
        assert profile.firstName is None
        assert profile.lastName is None
        assert profile.phone is None
        assert profile.address1 is None
        assert profile.address2 is None
        assert profile.city is None
        assert profile.zip is None
        assert profile.country is None

    def testProfileEmailValidation(self):
        """Test Profile email field validation."""
        # Valid emails
        validEmails = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ]
        
        for email in validEmails:
            profile = Profile(email=email)
            assert profile.email == email

        # Invalid emails
        invalidEmails = [
            "invalid_email",
            "@example.com",
            "user@",
            "user.example.com",
            ""
        ]
        
        for email in invalidEmails:
            with pytest.raises(ValidationError):
                Profile(email=email)

    def testProfileToDictMethod(self, validProfilePayload):
        """Test Profile toDict() method."""
        profile = Profile(**validProfilePayload)
        profileDict = profile.toDict()
        
        assert profileDict["email"] == "<EMAIL>"
        assert profileDict["title"] == "Mr."
        assert profileDict["firstName"] == "John"
        assert profileDict["lastName"] == "Doe"
        assert profileDict["phone"] == "+1234567890"
        assert profileDict["address1"] == "123 Main Street"
        assert profileDict["address2"] == "Apt 4B"
        assert profileDict["city"] == "New York"
        assert profileDict["zip"] == "10001"
        assert profileDict["country"] == "US"

    def testProfileToDictWithNoneValues(self):
        """Test Profile toDict() method with None values."""
        profile = Profile(
            email="<EMAIL>",
            firstName="John",
            lastName=None,
            phone=None
        )
        profileDict = profile.toDict()
        
        assert profileDict["email"] == "<EMAIL>"
        assert profileDict["firstName"] == "John"
        assert profileDict["lastName"] is None
        assert profileDict["phone"] is None

    def testProfileSerialization(self, validProfilePayload):
        """Test Profile JSON serialization."""
        profile = Profile(**validProfilePayload)
        
        # Test model_dump (Pydantic v2)
        profileData = profile.model_dump()
        assert profileData["email"] == "<EMAIL>"
        assert profileData["firstName"] == "John"
        
        # Test model_dump_json
        profileJson = profile.model_dump_json()
        assert isinstance(profileJson, str)
        assert "<EMAIL>" in profileJson

    def testProfileDeserialization(self, validProfilePayload):
        """Test Profile deserialization from dictionary."""
        profile = Profile(**validProfilePayload)
        profileDict = profile.model_dump()
        
        # Create new Profile from dictionary
        newProfile = Profile(**profileDict)
        assert newProfile.email == profile.email
        assert newProfile.firstName == profile.firstName
        assert newProfile.lastName == profile.lastName

    def testProfileFieldAliases(self):
        """Test Profile field aliases if any."""
        # Test that populate_by_name is configured
        assert Profile.model_config.get('populate_by_name') is True

    def testProfileOptionalFields(self):
        """Test Profile optional fields behavior."""
        profile = Profile(email="<EMAIL>")
        
        # All fields except email should be optional
        optionalFields = [
            'title', 'firstName', 'lastName', 'phone',
            'address1', 'address2', 'city', 'zip', 'country'
        ]
        
        for field in optionalFields:
            assert getattr(profile, field) is None

    def testProfileWithEmptyStrings(self):
        """Test Profile with empty strings for optional fields."""
        profile = Profile(
            email="<EMAIL>",
            title="",
            firstName="",
            lastName="",
            phone="",
            address1="",
            address2="",
            city="",
            zip="",
            country=""
        )
        
        # Empty strings should be preserved
        assert profile.title == ""
        assert profile.firstName == ""
        assert profile.lastName == ""

    def testProfileUpdate(self, validProfilePayload):
        """Test Profile field updates."""
        profile = Profile(**validProfilePayload)
        
        # Update fields
        originalEmail = profile.email
        profile.firstName = "Jane"
        profile.lastName = "Smith"
        
        assert profile.email == originalEmail  # Should remain unchanged
        assert profile.firstName == "Jane"
        assert profile.lastName == "Smith"

    def testProfileComparison(self, validProfilePayload):
        """Test Profile instance comparison."""
        profile1 = Profile(**validProfilePayload)
        profile2 = Profile(**validProfilePayload)
        
        # Should be equal (same data)
        assert profile1.model_dump() == profile2.model_dump()
        
        # Modify one profile
        profile2.firstName = "Jane"
        assert profile1.model_dump() != profile2.model_dump()

    def testProfileValidationErrors(self):
        """Test specific Profile validation error messages."""
        try:
            Profile(email="invalid_email")
        except ValidationError as e:
            errors = e.errors()
            emailError = next((err for err in errors if err['loc'] == ('email',)), None)
            assert emailError is not None
            assert 'email' in emailError['type'] or 'value_error' in emailError['type']

    def testProfileConfigurationSettings(self):
        """Test Profile Pydantic configuration settings."""
        # Test that the model has the expected configuration
        config = Profile.model_config
        assert config.get('populate_by_name') is True
        
        # Test that json_encoders is configured (even if empty)
        assert 'json_encoders' in config or hasattr(Profile, 'model_config')
