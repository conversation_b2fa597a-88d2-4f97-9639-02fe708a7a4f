"""Test cases for IncidentReference Pydantic BaseModel schema."""
import pytest
from datetime import datetime
from pydantic import ValidationError

from spacecommon.schemas.incidentReference import IncidentReference
from tests.testData.schemasData import (
    validIncidentReferencePayloadData,
    invalidIncidentReferencePayloadData,
    expectedIncidentReferenceDictOutput
)


class TestIncidentReferenceSchema:
    """Test class for IncidentReference Pydantic BaseModel schema."""

    def testValidIncidentReferenceCreation(self, validIncidentReferencePayload):
        """Test creating a valid IncidentReference instance."""
        incidentReference = IncidentReference(**validIncidentReferencePayload)
        assert incidentReference.incidentId == "inc_001"
        assert incidentReference.caseId == "case_001"
        assert incidentReference.email == "<EMAIL>"
        assert incidentReference.vin == "1HGBH41JXMN109186"
        assert incidentReference.brand == "Honda"
        assert incidentReference.country == "US"
        assert incidentReference.type == "service"
        assert incidentReference.title == "Oil Change Service"
        assert incidentReference.comment == "Regular maintenance oil change"
        assert isinstance(incidentReference.creationDate, datetime)

    def testInvalidIncidentReferenceCreation(self, invalidIncidentReferencePayload):
        """Test creating IncidentReference with invalid data should raise ValidationError."""
        with pytest.raises(ValidationError):
            IncidentReference(**invalidIncidentReferencePayload)

    def testIncidentReferenceWithDefaultCreationDate(self):
        """Test IncidentReference with default creationDate."""
        incidentReference = IncidentReference(
            incidentId="test_inc",
            email="<EMAIL>"
        )
        assert incidentReference.incidentId == "test_inc"
        assert incidentReference.email == "<EMAIL>"
        assert isinstance(incidentReference.creationDate, datetime)

    def testIncidentReferenceWithNoneValues(self):
        """Test IncidentReference with None values for optional fields."""
        incidentReference = IncidentReference()
        assert incidentReference.incidentId is None
        assert incidentReference.caseId is None
        assert incidentReference.email is None
        assert incidentReference.vin is None
        assert incidentReference.brand is None
        assert incidentReference.country is None
        assert incidentReference.type is None
        assert incidentReference.title is None
        assert incidentReference.comment is None
        assert isinstance(incidentReference.creationDate, datetime)  # Has default_factory

    def testIncidentReferenceToDictMethod(self, validIncidentReferencePayload):
        """Test IncidentReference toDict() method."""
        incidentReference = IncidentReference(**validIncidentReferencePayload)
        incidentReferenceDict = incidentReference.toDict()
        
        assert incidentReferenceDict["incidentId"] == "inc_001"
        assert incidentReferenceDict["caseId"] == "case_001"
        assert incidentReferenceDict["email"] == "<EMAIL>"
        assert incidentReferenceDict["vin"] == "1HGBH41JXMN109186"
        assert incidentReferenceDict["brand"] == "Honda"
        assert incidentReferenceDict["country"] == "US"
        assert incidentReferenceDict["type"] == "service"
        assert incidentReferenceDict["title"] == "Oil Change Service"
        assert incidentReferenceDict["comment"] == "Regular maintenance oil change"
        assert "creationDate" in incidentReferenceDict
        assert isinstance(incidentReferenceDict["creationDate"], str)  # ISO format

    def testIncidentReferenceDatetimeSerialization(self, validIncidentReferencePayload):
        """Test IncidentReference datetime serialization."""
        incidentReference = IncidentReference(**validIncidentReferencePayload)
        
        # Test model_dump
        incidentReferenceDict = incidentReference.model_dump()
        assert isinstance(incidentReferenceDict["creationDate"], datetime)
        
        # Test model_dump with mode='json' for serialization
        incidentReferenceJson = incidentReference.model_dump(mode='json')
        assert isinstance(incidentReferenceJson["creationDate"], str)

    def testIncidentReferenceDatetimeHandling(self):
        """Test IncidentReference datetime field handling."""
        testDatetime = datetime(2022, 6, 15, 14, 30, 0)
        incidentReference = IncidentReference(
            incidentId="test_inc",
            creationDate=testDatetime
        )
        
        assert incidentReference.creationDate == testDatetime
        
        # Test toDict() formats datetime as ISO string
        incidentReferenceDict = incidentReference.toDict()
        assert incidentReferenceDict["creationDate"] == testDatetime.isoformat()

    def testIncidentReferenceSerialization(self, validIncidentReferencePayload):
        """Test IncidentReference JSON serialization."""
        incidentReference = IncidentReference(**validIncidentReferencePayload)
        
        incidentReferenceJson = incidentReference.model_dump_json()
        assert isinstance(incidentReferenceJson, str)
        assert "inc_001" in incidentReferenceJson

    def testIncidentReferenceDeserialization(self, validIncidentReferencePayload):
        """Test IncidentReference deserialization from dictionary."""
        incidentReference = IncidentReference(**validIncidentReferencePayload)
        incidentReferenceDict = incidentReference.model_dump()
        
        # Create new IncidentReference from dictionary
        newIncidentReference = IncidentReference(**incidentReferenceDict)
        assert newIncidentReference.incidentId == incidentReference.incidentId
        assert newIncidentReference.caseId == incidentReference.caseId
        assert newIncidentReference.email == incidentReference.email

    def testIncidentReferenceConfigurationSettings(self):
        """Test IncidentReference Pydantic configuration settings."""
        config = IncidentReference.model_config
        assert config.get('populate_by_name') is True
        
        # Check json_encoders for datetime
        jsonEncoders = config.get('json_encoders', {})
        assert datetime in jsonEncoders or len(jsonEncoders) >= 0  # May be empty but should exist

    def testIncidentReferenceWithEmptyStrings(self):
        """Test IncidentReference with empty strings."""
        incidentReference = IncidentReference(
            incidentId="",
            caseId="",
            email="",
            vin="",
            brand="",
            country="",
            type="",
            title="",
            comment=""
        )
        
        # Empty strings should be preserved
        assert incidentReference.incidentId == ""
        assert incidentReference.caseId == ""
        assert incidentReference.email == ""

    def testIncidentReferenceUpdate(self, validIncidentReferencePayload):
        """Test IncidentReference field updates."""
        incidentReference = IncidentReference(**validIncidentReferencePayload)
        
        # Update fields
        originalIncidentId = incidentReference.incidentId
        incidentReference.incidentId = "updated_inc_001"
        incidentReference.title = "Updated Oil Change Service"
        
        assert incidentReference.incidentId == "updated_inc_001"
        assert incidentReference.title == "Updated Oil Change Service"
        assert originalIncidentId != incidentReference.incidentId

    def testIncidentReferenceComparison(self, validIncidentReferencePayload):
        """Test IncidentReference instance comparison."""
        incidentReference1 = IncidentReference(**validIncidentReferencePayload)
        incidentReference2 = IncidentReference(**validIncidentReferencePayload)
        
        # Should be equal (same data)
        assert incidentReference1.model_dump() == incidentReference2.model_dump()
        
        # Modify one instance
        incidentReference2.incidentId = "different_inc"
        assert incidentReference1.model_dump() != incidentReference2.model_dump()

    def testIncidentReferenceDatetimeValidation(self):
        """Test IncidentReference datetime field validation."""
        # Valid datetime
        validDatetime = datetime(2022, 1, 1, 10, 0, 0)
        incidentReference = IncidentReference(creationDate=validDatetime)
        assert incidentReference.creationDate == validDatetime
        
        # Invalid datetime should raise ValidationError
        with pytest.raises(ValidationError):
            IncidentReference(creationDate="invalid_date_string")
