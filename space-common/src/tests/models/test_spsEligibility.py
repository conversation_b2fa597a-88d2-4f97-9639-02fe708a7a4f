"""Test cases for SPSEligibility Beanie model."""
import pytest
from pydantic import ValidationError
from bson import ObjectId

from spacecommon.models.spsEligibility import SPSEligibility
from tests.testData.modelsData import (
    validSpsEligibilityPayloadData,
    invalidSpsEligibilityPayloadData,
    spsEligibilityDictPayloadData,
    expectedSpsEligibilityDictOutput
)


class TestSpsEligibilityModel:
    """Test class for SPSEligibility Beanie model."""

    @pytest.mark.asyncio
    async def testValidSpsEligibilityCreation(self, validSpsEligibilityPayload, mockDb):
        """Test creating a valid SPSEligibility instance."""
        spsEligibility = SPSEligibility(**validSpsEligibilityPayload)
        assert spsEligibility.scope == "global"
        assert spsEligibility.codes == ["SPS001", "SPS002", "SPS003"]
        assert spsEligibility.eligibilityRule == "vehicle_age < 5 AND mileage < 50000"
        assert spsEligibility.type == "warranty_extension"
        assert spsEligibility.eligibilityDisclaimer == "Terms and conditions apply"
        assert spsEligibility.name == "Extended Warranty Program"
        assert spsEligibility.description == "5-year extended warranty for eligible vehicles"
        assert spsEligibility.active is True

    @pytest.mark.asyncio
    async def testInvalidSpsEligibilityCreation(self, invalidSpsEligibilityPayload):
        """Test creating SPSEligibility with invalid data should raise ValidationError."""
        with pytest.raises(ValidationError):
            SPSEligibility(**invalidSpsEligibilityPayload)

    @pytest.mark.asyncio
    async def testSpsEligibilitySaveAndRetrieve(self, validSpsEligibilityPayload, mockDb):
        """Test saving and retrieving SPSEligibility from database."""
        spsEligibility = SPSEligibility(**validSpsEligibilityPayload)
        await spsEligibility.save()
        
        assert spsEligibility.id is not None
        
        # Retrieve the saved document
        retrievedSpsEligibility = await SPSEligibility.get(spsEligibility.id)
        assert retrievedSpsEligibility is not None
        assert retrievedSpsEligibility.scope == spsEligibility.scope
        assert retrievedSpsEligibility.codes == spsEligibility.codes
        assert retrievedSpsEligibility.eligibilityRule == spsEligibility.eligibilityRule

    @pytest.mark.asyncio
    async def testSpsEligibilityUpdate(self, validSpsEligibilityPayload, mockDb):
        """Test updating SPSEligibility document."""
        spsEligibility = SPSEligibility(**validSpsEligibilityPayload)
        await spsEligibility.save()
        
        # Update the document
        spsEligibility.active = False
        spsEligibility.scope = "regional"
        await spsEligibility.save()
        
        # Retrieve and verify update
        retrievedSpsEligibility = await SPSEligibility.get(spsEligibility.id)
        assert retrievedSpsEligibility.active is False
        assert retrievedSpsEligibility.scope == "regional"

    @pytest.mark.asyncio
    async def testSpsEligibilityDelete(self, validSpsEligibilityPayload, mockDb):
        """Test deleting SPSEligibility document."""
        spsEligibility = SPSEligibility(**validSpsEligibilityPayload)
        await spsEligibility.save()
        
        documentId = spsEligibility.id
        await spsEligibility.delete()
        
        # Verify document is deleted
        retrievedSpsEligibility = await SPSEligibility.get(documentId)
        assert retrievedSpsEligibility is None

    @pytest.mark.asyncio
    async def testSpsEligibilityFindByScope(self, validSpsEligibilityPayload, mockDb):
        """Test finding SPSEligibility by scope."""
        spsEligibility = SPSEligibility(**validSpsEligibilityPayload)
        await spsEligibility.save()
        
        # Find by scope
        foundSpsEligibility = await SPSEligibility.find_one(
            SPSEligibility.scope == "global"
        )
        assert foundSpsEligibility is not None
        assert foundSpsEligibility.scope == "global"

    @pytest.mark.asyncio
    async def testSpsEligibilityFindByType(self, validSpsEligibilityPayload, mockDb):
        """Test finding SPSEligibility by type."""
        spsEligibility = SPSEligibility(**validSpsEligibilityPayload)
        await spsEligibility.save()
        
        # Find by type
        foundSpsEligibility = await SPSEligibility.find_one(
            SPSEligibility.type == "warranty_extension"
        )
        assert foundSpsEligibility is not None
        assert foundSpsEligibility.type == "warranty_extension"

    @pytest.mark.asyncio
    async def testSpsEligibilityFindByActiveStatus(self, validSpsEligibilityPayload, mockDb):
        """Test finding SPSEligibility by active status."""
        spsEligibility = SPSEligibility(**validSpsEligibilityPayload)
        await spsEligibility.save()
        
        # Find active eligibilities
        activeEligibilities = await SPSEligibility.find(
            SPSEligibility.active == True
        ).to_list()
        assert len(activeEligibilities) >= 1
        assert all(eligibility.active is True for eligibility in activeEligibilities)

    def testSpsEligibilityToDictMethod(self, validSpsEligibilityPayload):
        """Test SPSEligibility toDict() method."""
        spsEligibility = SPSEligibility(**validSpsEligibilityPayload)
        spsEligibilityDict = spsEligibility.toDict()
        
        assert spsEligibilityDict["scope"] == "global"
        assert spsEligibilityDict["codes"] == ["SPS001", "SPS002", "SPS003"]
        assert spsEligibilityDict["eligibilityRule"] == "vehicle_age < 5 AND mileage < 50000"
        assert spsEligibilityDict["type"] == "warranty_extension"
        assert spsEligibilityDict["eligibilityDisclaimer"] == "Terms and conditions apply"
        assert spsEligibilityDict["name"] == "Extended Warranty Program"
        assert spsEligibilityDict["description"] == "5-year extended warranty for eligible vehicles"
        assert spsEligibilityDict["active"] is True

    def testSpsEligibilityFromDictMethod(self, spsEligibilityDictPayload):
        """Test SPSEligibility fromDict() class method."""
        spsEligibility = SPSEligibility.fromDict(spsEligibilityDictPayload)
        
        assert spsEligibility.scope == "regional"
        assert spsEligibility.codes == ["SPS004", "SPS005"]
        assert spsEligibility.eligibilityRule == "vehicle_age < 3"
        assert spsEligibility.type == "maintenance_package"
        assert spsEligibility.name == "Premium Maintenance Package"
        assert spsEligibility.active is False

    def testSpsEligibilityWithEmptyFields(self):
        """Test SPSEligibility with empty/None optional fields."""
        spsEligibility = SPSEligibility(
            scope=None,
            codes=[],
            eligibilityRule=None,
            type=None,
            eligibilityDisclaimer=None,
            name=None,
            description=None,
            active=None
        )
        
        assert spsEligibility.scope is None
        assert spsEligibility.codes == []
        assert spsEligibility.eligibilityRule is None
        assert spsEligibility.type is None
        assert spsEligibility.eligibilityDisclaimer is None
        assert spsEligibility.name is None
        assert spsEligibility.description is None
        assert spsEligibility.active is None

    def testSpsEligibilityWithMinimalData(self):
        """Test SPSEligibility with minimal required data."""
        spsEligibility = SPSEligibility()
        
        # All fields are optional, so this should work
        assert spsEligibility.scope is None
        assert spsEligibility.codes == []
        assert spsEligibility.eligibilityRule is None

    def testSpsEligibilityCodesListHandling(self):
        """Test SPSEligibility codes field list handling."""
        # Test with different types of codes
        spsEligibility1 = SPSEligibility(codes=["SPS001", "SPS002"])
        assert spsEligibility1.codes == ["SPS001", "SPS002"]
        
        spsEligibility2 = SPSEligibility(codes=[1, 2, 3])
        assert spsEligibility2.codes == [1, 2, 3]
        
        spsEligibility3 = SPSEligibility(codes=["mixed", 123, True])
        assert spsEligibility3.codes == ["mixed", 123, True]

    def testSpsEligibilitySettingsConfiguration(self):
        """Test SPSEligibility Beanie settings configuration."""
        assert SPSEligibility.Settings.name == "boSPSEligibility"
        assert SPSEligibility.Settings.use_state_management is True
        assert SPSEligibility.Settings.validate_on_save is True

    @pytest.mark.asyncio
    async def testSpsEligibilityBulkOperations(self, mockDb):
        """Test bulk operations with SPSEligibility."""
        spsEligibilityList = [
            SPSEligibility(
                scope=f"scope_{i}",
                codes=[f"SPS{i:03d}"],
                eligibilityRule=f"rule_{i}",
                type=f"type_{i}",
                name=f"Program {i}",
                description=f"Description for program {i}",
                active=i % 2 == 0
            )
            for i in range(5)
        ]
        
        # Bulk insert
        await SPSEligibility.insert_many(spsEligibilityList)
        
        # Verify all documents were inserted
        allSpsEligibilities = await SPSEligibility.find_all().to_list()
        assert len(allSpsEligibilities) >= 5
        
        # Find documents by scope pattern
        scopeEligibilities = await SPSEligibility.find(
            {"scope": {"$regex": "^scope_"}}
        ).to_list()
        assert len(scopeEligibilities) == 5

    @pytest.mark.asyncio
    async def testSpsEligibilityComplexQueries(self, mockDb):
        """Test complex queries with SPSEligibility."""
        # Create test data with different combinations
        testData = [
            SPSEligibility(
                scope="global",
                type="warranty",
                active=True,
                codes=["W001", "W002"]
            ),
            SPSEligibility(
                scope="regional",
                type="warranty",
                active=False,
                codes=["W003"]
            ),
            SPSEligibility(
                scope="global",
                type="maintenance",
                active=True,
                codes=["M001", "M002"]
            )
        ]
        
        await SPSEligibility.insert_many(testData)
        
        # Query for active global eligibilities
        activeGlobalEligibilities = await SPSEligibility.find(
            SPSEligibility.scope == "global",
            SPSEligibility.active == True
        ).to_list()
        assert len(activeGlobalEligibilities) == 2
        
        # Query for warranty type eligibilities
        warrantyEligibilities = await SPSEligibility.find(
            SPSEligibility.type == "warranty"
        ).to_list()
        assert len(warrantyEligibilities) == 2

    def testSpsEligibilityToDictWithNoneValues(self):
        """Test SPSEligibility toDict() method with None values."""
        spsEligibility = SPSEligibility(
            scope="test",
            codes=["TEST001"],
            eligibilityRule=None,
            type=None,
            active=True
        )
        
        spsEligibilityDict = spsEligibility.toDict()
        
        # None values should not be included in the dictionary
        assert "eligibilityRule" not in spsEligibilityDict
        assert "type" not in spsEligibilityDict
        assert spsEligibilityDict["scope"] == "test"
        assert spsEligibilityDict["codes"] == ["TEST001"]
        assert spsEligibilityDict["active"] is True
