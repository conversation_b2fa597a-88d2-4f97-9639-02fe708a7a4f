"""Test cases for ConsentArbitration Beanie model."""
import pytest
from pydantic import ValidationError
from bson import ObjectId

from spacecommon.models.consentArbitration import ConsentArbitration
from tests.testData.modelsData import (
    validConsentArbitrationPayloadData,
    invalidConsentArbitrationPayloadData,
    consentArbitrationDictPayloadData,
    expectedConsentArbitrationDictOutput
)


class TestConsentArbitrationModel:
    """Test class for ConsentArbitration Beanie model."""

    @pytest.mark.asyncio
    async def testValidConsentArbitrationCreation(self, validConsentArbitrationPayload, mockDb):
        """Test creating a valid ConsentArbitration instance."""
        consentArbitration = ConsentArbitration(**validConsentArbitrationPayload)
        assert consentArbitration.userId == "consent_user_123"
        assert consentArbitration.vin == "1HGBH41JXMN109186"
        assert consentArbitration.consent is True
        assert consentArbitration.brand == "Honda"
        assert consentArbitration.country == "US"
        assert consentArbitration.source == "mobile_app"
        assert consentArbitration.region == "north_america"
        assert consentArbitration.exported is False
        assert consentArbitration.createdAt == 1640995200
        assert consentArbitration.warrantyDocumentLink == "https://example.com/warranty.pdf"

    @pytest.mark.asyncio
    async def testInvalidConsentArbitrationCreation(self, invalidConsentArbitrationPayload):
        """Test creating ConsentArbitration with invalid data should raise ValidationError."""
        with pytest.raises(ValidationError):
            ConsentArbitration(**invalidConsentArbitrationPayload)

    @pytest.mark.asyncio
    async def testConsentArbitrationSaveAndRetrieve(self, validConsentArbitrationPayload, mockDb):
        """Test saving and retrieving ConsentArbitration from database."""
        consentArbitration = ConsentArbitration(**validConsentArbitrationPayload)
        await consentArbitration.save()
        
        assert consentArbitration.id is not None
        
        # Retrieve the saved document
        retrievedConsentArbitration = await ConsentArbitration.get(consentArbitration.id)
        assert retrievedConsentArbitration is not None
        assert retrievedConsentArbitration.userId == consentArbitration.userId
        assert retrievedConsentArbitration.vin == consentArbitration.vin
        assert retrievedConsentArbitration.consent == consentArbitration.consent

    @pytest.mark.asyncio
    async def testConsentArbitrationUpdate(self, validConsentArbitrationPayload, mockDb):
        """Test updating ConsentArbitration document."""
        consentArbitration = ConsentArbitration(**validConsentArbitrationPayload)
        await consentArbitration.save()
        
        # Update the document
        consentArbitration.consent = False
        consentArbitration.exported = True
        await consentArbitration.save()
        
        # Retrieve and verify update
        retrievedConsentArbitration = await ConsentArbitration.get(consentArbitration.id)
        assert retrievedConsentArbitration.consent is False
        assert retrievedConsentArbitration.exported is True

    @pytest.mark.asyncio
    async def testConsentArbitrationDelete(self, validConsentArbitrationPayload, mockDb):
        """Test deleting ConsentArbitration document."""
        consentArbitration = ConsentArbitration(**validConsentArbitrationPayload)
        await consentArbitration.save()
        
        documentId = consentArbitration.id
        await consentArbitration.delete()
        
        # Verify document is deleted
        retrievedConsentArbitration = await ConsentArbitration.get(documentId)
        assert retrievedConsentArbitration is None

    @pytest.mark.asyncio
    async def testConsentArbitrationFindByUserId(self, validConsentArbitrationPayload, mockDb):
        """Test finding ConsentArbitration by userId."""
        consentArbitration = ConsentArbitration(**validConsentArbitrationPayload)
        await consentArbitration.save()
        
        # Find by userId
        foundConsentArbitration = await ConsentArbitration.find_one(
            ConsentArbitration.userId == "consent_user_123"
        )
        assert foundConsentArbitration is not None
        assert foundConsentArbitration.userId == "consent_user_123"

    @pytest.mark.asyncio
    async def testConsentArbitrationFindByVin(self, validConsentArbitrationPayload, mockDb):
        """Test finding ConsentArbitration by VIN."""
        consentArbitration = ConsentArbitration(**validConsentArbitrationPayload)
        await consentArbitration.save()
        
        # Find by VIN
        foundConsentArbitration = await ConsentArbitration.find_one(
            ConsentArbitration.vin == "1HGBH41JXMN109186"
        )
        assert foundConsentArbitration is not None
        assert foundConsentArbitration.vin == "1HGBH41JXMN109186"

    @pytest.mark.asyncio
    async def testConsentArbitrationFindByConsent(self, validConsentArbitrationPayload, mockDb):
        """Test finding ConsentArbitration by consent status."""
        consentArbitration = ConsentArbitration(**validConsentArbitrationPayload)
        await consentArbitration.save()
        
        # Find by consent status
        consentedUsers = await ConsentArbitration.find(
            ConsentArbitration.consent == True
        ).to_list()
        assert len(consentedUsers) >= 1
        assert all(user.consent is True for user in consentedUsers)

    def testConsentArbitrationToDictMethod(self, validConsentArbitrationPayload):
        """Test ConsentArbitration toDict() method."""
        consentArbitration = ConsentArbitration(**validConsentArbitrationPayload)
        consentArbitrationDict = consentArbitration.toDict()
        
        assert consentArbitrationDict["userId"] == "consent_user_123"
        assert consentArbitrationDict["vin"] == "1HGBH41JXMN109186"
        assert consentArbitrationDict["consent"] is True
        assert consentArbitrationDict["brand"] == "Honda"
        assert consentArbitrationDict["country"] == "US"
        assert consentArbitrationDict["source"] == "mobile_app"
        assert consentArbitrationDict["region"] == "north_america"
        assert consentArbitrationDict["exported"] is False
        assert consentArbitrationDict["createdAt"] == 1640995200

    def testConsentArbitrationFromDictMethod(self, consentArbitrationDictPayload):
        """Test ConsentArbitration fromDict() class method."""
        consentArbitration = ConsentArbitration.fromDict(consentArbitrationDictPayload)
        
        assert consentArbitration.userId == "dict_consent_user_123"
        assert consentArbitration.vin == "2HGBH41JXMN109187"
        assert consentArbitration.consent is False
        assert consentArbitration.brand == "Toyota"
        assert consentArbitration.country == "CA"
        assert consentArbitration.exported is True

    def testConsentArbitrationRequiredFields(self):
        """Test ConsentArbitration with missing required fields."""
        with pytest.raises(ValidationError):
            ConsentArbitration()  # Missing all required fields

        with pytest.raises(ValidationError):
            ConsentArbitration(userId="test_user")  # Missing other required fields

    def testConsentArbitrationWithOptionalFields(self):
        """Test ConsentArbitration with optional warrantyDocumentLink."""
        # Without warranty document link
        consentArbitration1 = ConsentArbitration(
            userId="test_user",
            vin="1HGBH41JXMN109186",
            consent=True,
            brand="Honda",
            country="US",
            source="web",
            region="north_america",
            exported=False,
            createdAt=1640995200
        )
        assert consentArbitration1.warrantyDocumentLink is None

        # With warranty document link
        consentArbitration2 = ConsentArbitration(
            userId="test_user",
            vin="1HGBH41JXMN109186",
            consent=True,
            brand="Honda",
            country="US",
            source="web",
            region="north_america",
            exported=False,
            createdAt=1640995200,
            warrantyDocumentLink="https://example.com/warranty.pdf"
        )
        assert consentArbitration2.warrantyDocumentLink == "https://example.com/warranty.pdf"

    def testConsentArbitrationSettingsConfiguration(self):
        """Test ConsentArbitration Beanie settings configuration."""
        assert ConsentArbitration.Settings.name == "consentArbitration"
        assert ConsentArbitration.Settings.use_state_management is True
        assert ConsentArbitration.Settings.validate_on_save is True

    @pytest.mark.asyncio
    async def testConsentArbitrationBulkOperations(self, mockDb):
        """Test bulk operations with ConsentArbitration."""
        consentArbitrationList = [
            ConsentArbitration(
                userId=f"bulk_user_{i}",
                vin=f"VIN{i:010d}",
                consent=i % 2 == 0,  # Alternate consent status
                brand="Honda",
                country="US",
                source="bulk_test",
                region="north_america",
                exported=False,
                createdAt=1640995200 + i
            )
            for i in range(5)
        ]
        
        # Bulk insert
        await ConsentArbitration.insert_many(consentArbitrationList)
        
        # Verify all documents were inserted
        allConsentArbitrations = await ConsentArbitration.find_all().to_list()
        assert len(allConsentArbitrations) >= 5
        
        # Find documents by source
        bulkConsentArbitrations = await ConsentArbitration.find(
            ConsentArbitration.source == "bulk_test"
        ).to_list()
        assert len(bulkConsentArbitrations) == 5

    @pytest.mark.asyncio
    async def testConsentArbitrationIndexedFields(self, validConsentArbitrationPayload, mockDb):
        """Test indexed field functionality for userId."""
        consentArbitration = ConsentArbitration(**validConsentArbitrationPayload)
        await consentArbitration.save()
        
        # Test that indexed field (userId) can be used for efficient queries
        foundConsentArbitration = await ConsentArbitration.find_one(
            ConsentArbitration.userId == "consent_user_123"
        )
        assert foundConsentArbitration is not None
        assert foundConsentArbitration.userId == "consent_user_123"
