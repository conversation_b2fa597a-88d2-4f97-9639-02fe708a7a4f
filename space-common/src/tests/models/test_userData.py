"""Test cases for UserData Beanie model."""
import pytest
from pydantic import ValidationError
from bson import ObjectId

from spacecommon.models.userData import UserData
from spacecommon.schemas import Profile, Vehicle, UserPsaId, IncidentReference
from tests.testData.modelsData import (
    validUserDataPayloadData, 
    invalidUserDataPayloadData,
    userDataDictPayloadData,
    expectedUserDataDictOutput
)


class TestUserDataModel:
    """Test class for UserData Beanie model."""

    @pytest.mark.asyncio
    async def testValidUserDataCreation(self, validUserDataPayload, mockDb):
        """Test creating a valid UserData instance."""
        userData = UserData(**validUserDataPayload)
        assert userData.userId == "test_user_123"
        assert userData.userDbId == "db_user_456"
        assert len(userData.vehicle) == 1
        assert userData.vehicle[0].vin == "1HGBH41JXMN109186"
        assert len(userData.userPsaId) == 1
        assert userData.userPsaId[0].cvsId == "cvs_123"
        assert userData.profile.email == "<EMAIL>"

    @pytest.mark.asyncio
    async def testInvalidUserDataCreation(self, invalidUserDataPayload):
        """Test creating UserData with invalid data should raise ValidationError."""
        with pytest.raises(ValidationError):
            UserData(**invalidUserDataPayload)

    @pytest.mark.asyncio
    async def testUserDataSaveAndRetrieve(self, validUserDataPayload, mockDb):
        """Test saving and retrieving UserData from database."""
        userData = UserData(**validUserDataPayload)
        await userData.save()
        
        assert userData.id is not None
        
        # Retrieve the saved document
        retrievedUserData = await UserData.get(userData.id)
        assert retrievedUserData is not None
        assert retrievedUserData.userId == userData.userId
        assert retrievedUserData.userDbId == userData.userDbId

    @pytest.mark.asyncio
    async def testUserDataUpdate(self, validUserDataPayload, mockDb):
        """Test updating UserData document."""
        userData = UserData(**validUserDataPayload)
        await userData.save()
        
        # Update the document
        userData.userId = "updated_user_123"
        await userData.save()
        
        # Retrieve and verify update
        retrievedUserData = await UserData.get(userData.id)
        assert retrievedUserData.userId == "updated_user_123"

    @pytest.mark.asyncio
    async def testUserDataDelete(self, validUserDataPayload, mockDb):
        """Test deleting UserData document."""
        userData = UserData(**validUserDataPayload)
        await userData.save()
        
        documentId = userData.id
        await userData.delete()
        
        # Verify document is deleted
        retrievedUserData = await UserData.get(documentId)
        assert retrievedUserData is None

    @pytest.mark.asyncio
    async def testUserDataFindByUserId(self, validUserDataPayload, mockDb):
        """Test finding UserData by userId."""
        userData = UserData(**validUserDataPayload)
        await userData.save()
        
        # Find by userId
        foundUserData = await UserData.find_one(UserData.userId == "test_user_123")
        assert foundUserData is not None
        assert foundUserData.userId == "test_user_123"

    @pytest.mark.asyncio
    async def testUserDataToDictMethod(self, validUserDataPayload, mockDb):
        """Test UserData toDict() method."""
        userData = UserData(**validUserDataPayload)
        userDataDict = userData.toDict()
        
        assert userDataDict["userId"] == "test_user_123"
        assert userDataDict["userDbId"] == "db_user_456"
        assert len(userDataDict["vehicle"]) == 1
        assert userDataDict["vehicle"][0]["vin"] == "1HGBH41JXMN109186"
        assert userDataDict["profile"]["email"] == "<EMAIL>"

    @pytest.mark.asyncio
    async def testUserDataFromDictMethod(self, userDataDictPayload, mockDb):
        """Test UserData fromDict() class method."""
        userData = UserData.fromDict(userDataDictPayload)
        
        assert userData.userId == "dict_user_123"
        assert userData.userDbId == "dict_db_user_456"
        assert len(userData.vehicle) == 1
        assert userData.vehicle[0].vin == "2HGBH41JXMN109187"
        assert userData.vehicle[0].brand == "Toyota"

    @pytest.mark.asyncio
    async def testUserDataWithEmptyLists(self, mockDb):
        """Test UserData with empty lists."""
        userData = UserData(
            userId="test_user",
            userDbId="test_db_user",
            vehicle=[],
            userPsaId=[],
            incidents=[]
        )
        
        assert userData.userId == "test_user"
        assert len(userData.vehicle) == 0
        assert len(userData.userPsaId) == 0
        assert len(userData.incidents) == 0

    @pytest.mark.asyncio
    async def testUserDataWithNoneValues(self, mockDb):
        """Test UserData with None values for optional fields."""
        userData = UserData(
            userId="test_user",
            userDbId=None,
            preferredDealer=None,
            profile=None
        )
        
        assert userData.userId == "test_user"
        assert userData.userDbId is None
        assert userData.preferredDealer is None
        assert userData.profile is None

    @pytest.mark.asyncio
    async def testUserDataValidation(self, mockDb):
        """Test UserData field validation."""
        # Test with valid data
        userData = UserData(
            userId="valid_user",
            vehicle=[Vehicle(vin="1HGBH41JXMN109186", brand="Honda")]
        )
        await userData.save()
        assert userData.id is not None

    @pytest.mark.asyncio
    async def testUserDataSettingsConfiguration(self, mockDb):
        """Test UserData Beanie settings configuration."""
        assert UserData.Settings.name == "userData"
        assert UserData.Settings.use_state_management is True
        assert UserData.Settings.validate_on_save is True

    @pytest.mark.asyncio
    async def testUserDataBulkOperations(self, mockDb):
        """Test bulk operations with UserData."""
        userDataList = [
            UserData(userId=f"bulk_user_{i}", userDbId=f"bulk_db_{i}")
            for i in range(3)
        ]
        
        # Bulk insert
        await UserData.insert_many(userDataList)
        
        # Verify all documents were inserted
        allUserData = await UserData.find_all().to_list()
        assert len(allUserData) >= 3
        
        # Find specific documents
        bulkUsers = await UserData.find(
            {"userId": {"$regex": "^bulk_user_"}}
        ).to_list()
        assert len(bulkUsers) == 3
