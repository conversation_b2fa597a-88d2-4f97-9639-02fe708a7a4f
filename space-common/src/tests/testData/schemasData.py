"""Test data for Pydantic BaseModel schemas."""
from datetime import datetime

# Profile test data
validProfilePayloadData = {
    "email": "<EMAIL>",
    "title": "Mr.",
    "firstName": "<PERSON>",
    "lastName": "Doe",
    "phone": "+1234567890",
    "address1": "123 Main Street",
    "address2": "Apt 4B",
    "city": "New York",
    "zip": "10001",
    "country": "US"
}

invalidProfilePayloadData = {
    "email": "invalid_email_format",  # Invalid email
    "title": "",
    "firstName": "",
    "lastName": "",
    "phone": "",
    "address1": "",
    "city": "",
    "zip": "",
    "country": ""
}

# MileageData test data
validMileageDataPayloadData = {
    "value": 25000,
    "date": 1640995200,  # Unix timestamp
    "timestamp": 1640995200,
    "unit": "km"
}

invalidMileageDataPayloadData = {
    "value": "invalid_number",  # Should be integer
    "date": "invalid_timestamp",  # Should be integer
    "timestamp": "invalid_timestamp",  # Should be integer
    "unit": ""  # Should not be empty
}

# VehicleOrder test data
validVehicleOrderPayloadData = {
    "mopId": "mop_12345",
    "orderFormId": "order_67890",
    "orderFormStatus": "pending",
    "trackingStatus": "processing",
    "isUpdated": True
}

invalidVehicleOrderPayloadData = {
    "mopId": "",
    "orderFormId": "",
    "orderFormStatus": "",
    "trackingStatus": "",
    "isUpdated": "not_boolean"  # Should be boolean
}

# Vehicle test data
validVehiclePayloadData = {
    "documentId": "doc_123456",
    "vin": "1HGBH41JXMN109186",
    "label": "My Honda Civic",
    "brand": "Honda",
    "model": "Civic",
    "modelDescription": "Honda Civic LX 4-Door Sedan",
    "version": "LX",
    "versionId": "v_001",
    "registrationNumber": "ABC123",
    "registrationDate": datetime(2021, 1, 15),
    "nickName": "Blue Beast",
    "picture": "https://example.com/car.jpg",
    "color": "Blue",
    "energy": "Gasoline",
    "type": "Sedan",
    "sdp": "active",
    "shortLabel": "Civic LX",
    "lastUpdate": 1640995200,
    "year": "2021",
    "country": "US",
    "market": "North America",
    "regTimeStamp": 1610668800,
    "warrantyStartDate": "2021-01-15",
    "make": "Honda",
    "subMake": "Acura",
    "enrollmentStatus": "enrolled",
    "connectorType": "standard",
    "addStatus": "active",
    "isO2x": False,
    "plugType": ["type1", "type2"],
    "featureCodeExpiry": 1672531200,
    "visual": "sedan_blue",
    "language": "en-US",
    "featureCode": ["FC001", "FC002", "FC003"],
    "preferredDealer": {
        "dealerId": "dealer_001",
        "name": "Honda Downtown"
    },
    "isOrder": False,
    "vehicleOrder": {
        "mopId": "mop_12345",
        "orderFormId": "order_67890",
        "orderFormStatus": "completed",
        "trackingStatus": "delivered",
        "isUpdated": True
    },
    "mileage": {
        "value": 25000,
        "date": 1640995200,
        "timestamp": 1640995200,
        "unit": "km"
    }
}

invalidVehiclePayloadData = {
    "documentId": "",
    "vin": "",
    "registrationDate": "invalid_date_format",  # Should be datetime
    "lastUpdate": "invalid_timestamp",  # Should be integer
    "year": "",
    "isO2x": "not_boolean",  # Should be boolean
    "plugType": "not_a_list",  # Should be list
    "featureCodeExpiry": "invalid_timestamp",  # Should be integer
    "featureCode": "not_a_list",  # Should be list
    "isOrder": "not_boolean"  # Should be boolean
}

# UserPsaId test data
validUserPsaIdPayloadData = {
    "cvsId": "cvs_123456",
    "brand": "Honda"
}

invalidUserPsaIdPayloadData = {
    "cvsId": "",
    "brand": ""
}

# IncidentReference test data
validIncidentReferencePayloadData = {
    "incidentId": "inc_001",
    "caseId": "case_001",
    "email": "<EMAIL>",
    "vin": "1HGBH41JXMN109186",
    "brand": "Honda",
    "country": "US",
    "type": "service",
    "title": "Oil Change Service",
    "comment": "Regular maintenance oil change",
    "creationDate": datetime(2022, 1, 1, 10, 0, 0)
}

invalidIncidentReferencePayloadData = {
    "incidentId": "",
    "caseId": "",
    "email": "invalid_email",  # Invalid email format
    "vin": "",
    "brand": "",
    "country": "",
    "type": "",
    "title": "",
    "comment": "",
    "creationDate": "invalid_date"  # Should be datetime
}

# Expected outputs for toDict() method tests
expectedProfileDictOutput = {
    "email": "<EMAIL>",
    "title": "Mr.",
    "firstName": "John",
    "lastName": "Doe",
    "phone": "+1234567890",
    "address1": "123 Main Street",
    "address2": "Apt 4B",
    "city": "New York",
    "zip": "10001",
    "country": "US"
}

expectedMileageDataDictOutput = {
    "value": 25000,
    "date": 1640995200,
    "timestamp": 1640995200,
    "unit": "km"
}

expectedVehicleOrderDictOutput = {
    "mopId": "mop_12345",
    "orderFormId": "order_67890",
    "orderFormStatus": "pending",
    "trackingStatus": "processing",
    "isUpdated": True
}

expectedUserPsaIdDictOutput = {
    "cvsId": "cvs_123456",
    "brand": "Honda"
}

expectedIncidentReferenceDictOutput = {
    "incidentId": "inc_001",
    "caseId": "case_001",
    "email": "<EMAIL>",
    "vin": "1HGBH41JXMN109186",
    "brand": "Honda",
    "country": "US",
    "type": "service",
    "title": "Oil Change Service",
    "comment": "Regular maintenance oil change",
    "creationDate": "2022-01-01T10:00:00"
}
