"""Test data for Beanie models."""
from datetime import datetime
from bson import ObjectId

# UserData test data
validUserDataPayloadData = {
    "userId": "test_user_123",
    "userDbId": "db_user_456",
    "vehicle": [
        {
            "vin": "1HGBH41JXMN109186",
            "brand": "Honda",
            "model": "Civic",
            "year": "2021",
            "registrationNumber": "ABC123"
        }
    ],
    "userPsaId": [
        {
            "cvsId": "cvs_123",
            "brand": "Honda"
        }
    ],
    "incidents": [
        {
            "incidentId": "inc_001",
            "caseId": "case_001",
            "email": "<EMAIL>",
            "vin": "1HGBH41JXMN109186",
            "brand": "Honda",
            "country": "US",
            "type": "service",
            "title": "Oil Change",
            "comment": "Regular maintenance"
        }
    ],
    "preferredDealer": {
        "Honda": {
            "dealerId": "dealer_001",
            "name": "Honda Downtown",
            "address": "123 Main St"
        }
    },
    "profile": {
        "email": "<EMAIL>",
        "firstName": "<PERSON>",
        "lastName": "Doe",
        "phone": "+1234567890",
        "address1": "123 Test St",
        "city": "Test City",
        "zip": "12345",
        "country": "US"
    }
}

invalidUserDataPayloadData = {
    "userId": "",  # Invalid empty string
    "userDbId": None,
    "vehicle": "invalid_vehicle_data",  # Should be list
    "profile": {
        "email": "invalid_email",  # Invalid email format
        "firstName": "",
        "lastName": ""
    }
}

userDataDictPayloadData = {
    "_id": "507f1f77bcf86cd799439011",
    "userId": "dict_user_123",
    "userDbId": "dict_db_user_456",
    "vehicle": [
        {
            "vin": "2HGBH41JXMN109187",
            "brand": "Toyota",
            "model": "Camry"
        }
    ],
    "userPsaId": [
        {
            "cvsId": "dict_cvs_123",
            "brand": "Toyota"
        }
    ]
}

# ConsentArbitration test data
validConsentArbitrationPayloadData = {
    "userId": "consent_user_123",
    "vin": "1HGBH41JXMN109186",
    "consent": True,
    "brand": "Honda",
    "country": "US",
    "source": "mobile_app",
    "region": "north_america",
    "exported": False,
    "createdAt": 1640995200,  # Unix timestamp
    "warrantyDocumentLink": "https://example.com/warranty.pdf"
}

invalidConsentArbitrationPayloadData = {
    "userId": "",  # Required field cannot be empty
    "vin": "",     # Required field cannot be empty
    "consent": "invalid_boolean",  # Should be boolean
    "brand": None,  # Required field cannot be None
    "country": "",  # Required field cannot be empty
    "source": "",   # Required field cannot be empty
    "region": "",   # Required field cannot be empty
    "exported": "not_boolean",  # Should be boolean
    "createdAt": "invalid_timestamp"  # Should be integer
}

consentArbitrationDictPayloadData = {
    "_id": "507f1f77bcf86cd799439012",
    "userId": "dict_consent_user_123",
    "vin": "2HGBH41JXMN109187",
    "consent": False,
    "brand": "Toyota",
    "country": "CA",
    "source": "web_portal",
    "region": "north_america",
    "exported": True,
    "createdAt": 1640995300,
    "warrantyDocumentLink": None
}

# SPSEligibility test data
validSpsEligibilityPayloadData = {
    "scope": "global",
    "codes": ["SPS001", "SPS002", "SPS003"],
    "eligibilityRule": "vehicle_age < 5 AND mileage < 50000",
    "type": "warranty_extension",
    "eligibilityDisclaimer": "Terms and conditions apply",
    "name": "Extended Warranty Program",
    "description": "5-year extended warranty for eligible vehicles",
    "active": True
}

invalidSpsEligibilityPayloadData = {
    "scope": "",
    "codes": "invalid_codes_format",  # Should be list
    "eligibilityRule": None,
    "type": "",
    "eligibilityDisclaimer": "",
    "name": "",
    "description": "",
    "active": "not_boolean"  # Should be boolean
}

spsEligibilityDictPayloadData = {
    "_id": "507f1f77bcf86cd799439013",
    "scope": "regional",
    "codes": ["SPS004", "SPS005"],
    "eligibilityRule": "vehicle_age < 3",
    "type": "maintenance_package",
    "eligibilityDisclaimer": "Valid for participating dealers only",
    "name": "Premium Maintenance Package",
    "description": "Comprehensive maintenance for new vehicles",
    "active": False
}

# Expected outputs for toDict() method tests
expectedUserDataDictOutput = {
    "userId": "test_user_123",
    "userDbId": "db_user_456",
    "vehicle": [
        {
            "vin": "1HGBH41JXMN109186",
            "brand": "Honda",
            "model": "Civic",
            "year": "2021",
            "registrationNumber": "ABC123"
        }
    ],
    "userPsaId": [
        {
            "cvsId": "cvs_123",
            "brand": "Honda"
        }
    ],
    "incidents": [
        {
            "incidentId": "inc_001",
            "caseId": "case_001",
            "email": "<EMAIL>",
            "vin": "1HGBH41JXMN109186",
            "brand": "Honda",
            "country": "US",
            "type": "service",
            "title": "Oil Change",
            "comment": "Regular maintenance"
        }
    ],
    "preferredDealer": {
        "Honda": {
            "dealerId": "dealer_001",
            "name": "Honda Downtown",
            "address": "123 Main St"
        }
    },
    "profile": {
        "email": "<EMAIL>",
        "firstName": "John",
        "lastName": "Doe",
        "phone": "+1234567890",
        "address1": "123 Test St",
        "city": "Test City",
        "zip": "12345",
        "country": "US"
    }
}

expectedConsentArbitrationDictOutput = {
    "userId": "consent_user_123",
    "vin": "1HGBH41JXMN109186",
    "consent": True,
    "brand": "Honda",
    "country": "US",
    "source": "mobile_app",
    "region": "north_america",
    "exported": False,
    "createdAt": 1640995200,
    "warrantyDocumentLink": "https://example.com/warranty.pdf"
}

expectedSpsEligibilityDictOutput = {
    "scope": "global",
    "codes": ["SPS001", "SPS002", "SPS003"],
    "eligibilityRule": "vehicle_age < 5 AND mileage < 50000",
    "type": "warranty_extension",
    "eligibilityDisclaimer": "Terms and conditions apply",
    "name": "Extended Warranty Program",
    "description": "5-year extended warranty for eligible vehicles",
    "active": True
}
