"""Pytest configuration and fixtures for space-common tests."""
import pytest
import pytest_asyncio
import asyncio
from datetime import datetime, timed<PERSON>ta
from typing import AsyncGenerator
from motor.motor_asyncio import AsyncIOMotorClient
from beanie import init_beanie
from mongomock_motor import AsyncMongoMockClient

# Import all models and schemas for testing
from spacecommon.models import UserData, ConsentArbitration, SPSEligibility
from spacecommon.schemas import (
    Profile, MileageData, VehicleOrder, 
    Vehicle, UserPsaId, IncidentReference
)

# Import test data
from tests.testData.modelsData import *
from tests.testData.schemasData import *


@pytest.fixture(scope="session")
def eventLoop():
    """Create an event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest_asyncio.fixture(scope="function")
async def mockDb():
    """Create a mock MongoDB database for testing."""
    client = AsyncMongoMockClient()
    database = client.testDb
    
    # Initialize Beanie with mock database
    await init_beanie(
        database=database,
        document_models=[UserData, ConsentArbitration, SPSEligibility]
    )
    
    yield database
    
    # Cleanup
    if hasattr(client, 'close') and callable(client.close):
        try:
            await client.close()
        except:
            pass  # Ignore cleanup errors


# UserData test fixtures
@pytest.fixture
def validUserDataPayload():
    """Valid UserData payload for testing."""
    return validUserDataPayloadData


@pytest.fixture
def invalidUserDataPayload():
    """Invalid UserData payload for testing."""
    return invalidUserDataPayloadData


@pytest.fixture
def userDataDictPayload():
    """UserData dictionary payload for testing."""
    return userDataDictPayloadData


# ConsentArbitration test fixtures
@pytest.fixture
def validConsentArbitrationPayload():
    """Valid ConsentArbitration payload for testing."""
    return validConsentArbitrationPayloadData


@pytest.fixture
def invalidConsentArbitrationPayload():
    """Invalid ConsentArbitration payload for testing."""
    return invalidConsentArbitrationPayloadData


@pytest.fixture
def consentArbitrationDictPayload():
    """ConsentArbitration dictionary payload for testing."""
    return consentArbitrationDictPayloadData


# SPSEligibility test fixtures
@pytest.fixture
def validSpsEligibilityPayload():
    """Valid SPSEligibility payload for testing."""
    return validSpsEligibilityPayloadData


@pytest.fixture
def invalidSpsEligibilityPayload():
    """Invalid SPSEligibility payload for testing."""
    return invalidSpsEligibilityPayloadData


@pytest.fixture
def spsEligibilityDictPayload():
    """SPSEligibility dictionary payload for testing."""
    return spsEligibilityDictPayloadData


# Profile schema test fixtures
@pytest.fixture
def validProfilePayload():
    """Valid Profile payload for testing."""
    return validProfilePayloadData


@pytest.fixture
def invalidProfilePayload():
    """Invalid Profile payload for testing."""
    return invalidProfilePayloadData


# MileageData schema test fixtures
@pytest.fixture
def validMileageDataPayload():
    """Valid MileageData payload for testing."""
    return validMileageDataPayloadData


@pytest.fixture
def invalidMileageDataPayload():
    """Invalid MileageData payload for testing."""
    return invalidMileageDataPayloadData


# VehicleOrder schema test fixtures
@pytest.fixture
def validVehicleOrderPayload():
    """Valid VehicleOrder payload for testing."""
    return validVehicleOrderPayloadData


@pytest.fixture
def invalidVehicleOrderPayload():
    """Invalid VehicleOrder payload for testing."""
    return invalidVehicleOrderPayloadData


# Vehicle schema test fixtures
@pytest.fixture
def validVehiclePayload():
    """Valid Vehicle payload for testing."""
    return validVehiclePayloadData


@pytest.fixture
def invalidVehiclePayload():
    """Invalid Vehicle payload for testing."""
    return invalidVehiclePayloadData


# UserPsaId schema test fixtures
@pytest.fixture
def validUserPsaIdPayload():
    """Valid UserPsaId payload for testing."""
    return validUserPsaIdPayloadData


@pytest.fixture
def invalidUserPsaIdPayload():
    """Invalid UserPsaId payload for testing."""
    return invalidUserPsaIdPayloadData


# IncidentReference schema test fixtures
@pytest.fixture
def validIncidentReferencePayload():
    """Valid IncidentReference payload for testing."""
    return validIncidentReferencePayloadData


@pytest.fixture
def invalidIncidentReferencePayload():
    """Invalid IncidentReference payload for testing."""
    return invalidIncidentReferencePayloadData
