from setuptools import find_packages, setup
setup(
    name='space-commons',
    packages=find_packages(where="src", exclude=["src/tests/", "/tests"]),
    package_dir={"": "src"},
    version='0.0.1',
    description='Common functionality across Space microservices will be provided by this module',
    author='<EMAIL>',
    install_requires=[
            "pydantic==2.8.2",
            "email-validator==2.3.0",
            "beanie==1.26.0",
            "motor==3.7.1"
      ]
)
