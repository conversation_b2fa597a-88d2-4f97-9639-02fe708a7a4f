FROM python:3.12 as base
WORKDIR /code

FROM base as dependencies
COPY ./requirements.txt /code/requirements.txt
RUN pip install --no-cache-dir --upgrade -r /code/requirements.txt

# ---- Copy Files/Build ----
FROM dependencies AS build  
WORKDIR /code
COPY . .


FROM build as test
RUN python3 -m pylint /code/core --list-msgs --output-format=parseable --output=pylint.log --fail-under=1
RUN python -m pytest --cov-report xml --cov=. src/tests/ --junit-xml unit_tests.xml
RUN sed -i 's#/code#.#g' coverage.xml


FROM python:3.12 AS release
WORKDIR /code
COPY --from=dependencies /code/requirements.txt ./
# Install app dependencies
RUN pip install -r requirements.txt
COPY --from=build /code/ ./
RUN python setup.py bdist_wheel